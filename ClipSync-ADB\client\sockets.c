#include "sockets.h"
#include <stdio.h>
#include <windows.h> // for Sleep

#ifdef _WIN32

bool net_init(void) {
    WSADATA wsa_data;
    return WSAStartup(MAKEWORD(2, 2), &wsa_data) == 0;
}

SC_SOCKET_T net_connect(uint32_t ip, uint16_t port) {
    SC_SOCKET_T sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock == SC_INVALID_SOCKET) {
        return SC_INVALID_SOCKET;
    }

    struct sockaddr_in sin;
    sin.sin_family = AF_INET;
    sin.sin_addr.s_addr = htonl(ip);
    sin.sin_port = htons(port);

    if (connect(sock, (struct sockaddr *)&sin, sizeof(sin)) == -1) {
        closesocket(sock);
        return SC_INVALID_SOCKET;
    }

    return sock;
}

// Kết nối với retry logic như scrcpy-master
SC_SOCKET_T net_connect_with_retry(uint32_t ip, uint16_t port, unsigned attempts, unsigned delay_ms) {
    for (unsigned i = 0; i < attempts; i++) {
        if (i > 0) {
            printf("   -> Lan thu %u/%u...\n", i + 1, attempts);
        }

        SC_SOCKET_T sock = socket(AF_INET, SOCK_STREAM, 0);
        if (sock == SC_INVALID_SOCKET) {
            if (i < attempts - 1) {
                Sleep(delay_ms);
                continue;
            }
            return SC_INVALID_SOCKET;
        }

        // Set socket timeout để tránh hang
        DWORD timeout = 5000; // 5 seconds
        setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout));
        setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout));

        struct sockaddr_in sin;
        sin.sin_family = AF_INET;
        sin.sin_addr.s_addr = htonl(ip);
        sin.sin_port = htons(port);

        if (connect(sock, (struct sockaddr *)&sin, sizeof(sin)) == 0) {
            // Kết nối thành công
            return sock;
        }

        // Kết nối thất bại
        int error = WSAGetLastError();
        closesocket(sock);

        if (i < attempts - 1) {
            printf("   -> Ket noi that bai (error %d), thu lai sau %ums...\n", error, delay_ms);
            Sleep(delay_ms);
        } else {
            printf("   -> Ket noi that bai sau %u lan thu (error %d)\n", attempts, error);
        }
    }

    return SC_INVALID_SOCKET;
}

bool net_close(SC_SOCKET_T socket) {
    return closesocket(socket) == 0;
}

int net_send_all(SC_SOCKET_T socket, const void *buf, size_t len) {
    const char *p = buf;
    while (len > 0) {
        int w = send(socket, p, len, 0);
        if (w == -1) {
            return -1;
        }
        len -= w;
        p += w;
    }
    return 0;
}

int net_recv_all(SC_SOCKET_T socket, void *buf, size_t len) {
    char *p = buf;
    while (len > 0) {
        int r = recv(socket, p, len, 0);
        if (r == -1) {
            return -1;
        }
        if (r == 0) {
            // connection closed
            return 1;
        }
        len -= r;
        p += r;
    }
    return 0;
}


#else

// POSIX implementation would go here

#endif 