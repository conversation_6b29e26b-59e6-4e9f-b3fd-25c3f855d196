#include "sockets.h"
#include <stdio.h>

#ifdef _WIN32

bool net_init(void) {
    WSADATA wsa_data;
    return WSAStartup(MAKEWORD(2, 2), &wsa_data) == 0;
}

SC_SOCKET_T net_connect(uint32_t ip, uint16_t port) {
    SC_SOCKET_T sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock == SC_INVALID_SOCKET) {
        return SC_INVALID_SOCKET;
    }

    struct sockaddr_in sin;
    sin.sin_family = AF_INET;
    sin.sin_addr.s_addr = htonl(ip);
    sin.sin_port = htons(port);

    if (connect(sock, (struct sockaddr *)&sin, sizeof(sin)) == -1) {
        closesocket(sock);
        return SC_INVALID_SOCKET;
    }

    return sock;
}

bool net_close(SC_SOCKET_T socket) {
    return closesocket(socket) == 0;
}

int net_send_all(SC_SOCKET_T socket, const void *buf, size_t len) {
    const char *p = buf;
    while (len > 0) {
        int w = send(socket, p, len, 0);
        if (w == -1) {
            return -1;
        }
        len -= w;
        p += w;
    }
    return 0;
}

int net_recv_all(SC_SOCKET_T socket, void *buf, size_t len) {
    char *p = buf;
    while (len > 0) {
        int r = recv(socket, p, len, 0);
        if (r == -1) {
            return -1;
        }
        if (r == 0) {
            // connection closed
            return 1;
        }
        len -= r;
        p += r;
    }
    return 0;
}


#else

// POSIX implementation would go here

#endif 