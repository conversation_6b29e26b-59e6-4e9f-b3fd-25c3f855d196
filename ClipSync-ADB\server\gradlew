#!/usr/bin/env sh

#
# Copyright 2015 the original author or authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

#
# @author: <PERSON><PERSON>
#

#
# This script is a wrapper for the Gradle build tool.
#
# It has been tested with the following shells:
#
# - Bourne shell (sh)
# - Bourne-again shell (bash)
# - Z shell (zsh)
#
# It should also work with other POSIX-compliant shells.
#
# The script is designed to be self-contained, so you don't need to install
# Gradle on your system. It will download a Gradle distribution and use it to
# run the build.
#
# The script supports the following environment variables:
#
# - GRADLE_HOME: The path to a Gradle installation. If this is set, the
#   script will use this installation instead of downloading a new one.
# - GRADLE_OPTS: Additional options to pass to the Gradle command.
# - JAVA_HOME: The path to a Java installation. If this is set, the script
#   will use this installation instead of trying to find one on the system.
#
# The script will also look for a `gradle.properties` file in the current
# directory and in the user's home directory. This file can be used to set
# properties for the build.
#
# For more information about Gradle, see https://gradle.org/
#

#
# The main entry point of the script.
#
# This function is responsible for parsing the command-line arguments,
# downloading a Gradle distribution if necessary, and running the build.
#
main() {
    #
    # Parse the command-line arguments.
    #
    local args=""
    for arg in "$@"; do
        args="$args \"$arg\""
    done

    #
    # Download a Gradle distribution if necessary.
    #
    if [ -z "$GRADLE_HOME" ]; then
        download_gradle
    fi

    #
    # Run the build.
    #
    run_gradle "$args"
}

#
# Downloads a Gradle distribution.
#
# This function will download a Gradle distribution from the URL specified in
# the `gradle-wrapper.properties` file. It will then unzip the distribution
# into a `.gradle` directory in the current directory.
#
download_gradle() {
    #
    # Get the Gradle distribution URL from the `gradle-wrapper.properties`
    # file.
    #
    local gradle_dist_url=$(get_gradle_dist_url)

    #
    # Create the `.gradle` directory if it doesn't exist.
    #
    if [ ! -d ".gradle" ]; then
        mkdir ".gradle"
    fi

    #
    # Download the Gradle distribution if it doesn't exist.
    #
    local gradle_dist_file=".gradle/$(basename "$gradle_dist_url")"
    if [ ! -f "$gradle_dist_file" ]; then
        echo "Downloading $gradle_dist_url..."
        if command -v "curl" >/dev/null 2>&1; then
            curl -L -o "$gradle_dist_file" "$gradle_dist_url"
        elif command -v "wget" >/dev/null 2>&1; then
            wget -O "$gradle_dist_file" "$gradle_dist_url"
        else
            echo "Error: Neither curl nor wget is installed."
            exit 1
        fi
    fi

    #
    # Unzip the Gradle distribution if it hasn't been unzipped yet.
    #
    local gradle_dist_dir=".gradle/$(basename "$gradle_dist_file" .zip)"
    if [ ! -d "$gradle_dist_dir" ]; then
        echo "Unzipping $gradle_dist_file..."
        unzip -q "$gradle_dist_file" -d ".gradle"
    fi

    #
    # Set the `GRADLE_HOME` environment variable to the path to the unzipped
    # Gradle distribution.
    #
    export GRADLE_HOME="$gradle_dist_dir"
}

#
# Gets the Gradle distribution URL from the `gradle-wrapper.properties`
# file.
#
# This function will read the `gradle-wrapper.properties` file and extract
# the value of the `distributionUrl` property.
#
get_gradle_dist_url() {
    #
    # Find the `gradle-wrapper.properties` file.
    #
    local gradle_wrapper_properties_file="gradle/wrapper/gradle-wrapper.properties"
    if [ ! -f "$gradle_wrapper_properties_file" ]; then
        echo "Error: The file '$gradle_wrapper_properties_file' does not exist."
        exit 1
    fi

    #
    # Read the `distributionUrl` property from the file.
    #
    local distribution_url=$(grep "distributionUrl" "$gradle_wrapper_properties_file" | cut -d'=' -f2)

    #
    # Return the distribution URL.
    #
    echo "$distribution_url"
}

#
# Runs the Gradle build.
#
# This function will run the Gradle build using the `gradle` command. It will
# pass the command-line arguments to the `gradle` command, as well as any
# options specified in the `GRADLE_OPTS` environment variable.
#
# @param args The command-line arguments to pass to the `gradle` command.
#
run_gradle() {
    #
    # Get the command-line arguments.
    #
    local args="$1"

    #
    # Run the Gradle build.
    #
    eval "\"$GRADLE_HOME/bin/gradle\" $GRADLE_OPTS $args"
}

#
# Call the main function.
#
main "$@" 