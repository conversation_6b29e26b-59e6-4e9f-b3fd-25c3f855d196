package com.clipsyncadb;

import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;

public final class DesktopConnection implements Runnable, Closeable {

    private final Socket socket;
    private final InputStream inputStream;
    private final OutputStream outputStream;
    private final ClipboardSync clipboardSync;

    public DesktopConnection(Socket socket) throws IOException {
        this.socket = socket;
        this.inputStream = socket.getInputStream();
        this.outputStream = socket.getOutputStream();
        this.clipboardSync = new ClipboardSync(this);
    }

    @Override
    public void run() {
        // First, read the dummy byte from the client
        try {
            int r = inputStream.read();
            if (r == -1) {
                return; // client disconnected
            }
        } catch (IOException e) {
            return; // could not read
        }

        clipboardSync.start();

        try {
            while (true) {
                DeviceMessage message = DeviceMessage.from(inputStream);
                if (message == null) {
                    break;
                }
                handleMessage(message);
            }
        } catch (IOException e) {
            // expected on close
        } finally {
            close();
        }
    }

    private void handleMessage(DeviceMessage message) {
        switch (message.getType()) {
            case DeviceMessage.TYPE_SET_CLIPBOARD:
                clipboardSync.setClipboard(message.getText(), message.getSequence());
                break;
            // Other cases can be added here
        }
    }
    
    public synchronized void sendClipboard(String text) throws IOException {
        ControlMessage.createSetClipboard(text).sendTo(outputStream);
    }

    @Override
    public void close() {
        try {
            socket.close();
        } catch (IOException e) {
            // ignore
        }
    }
} 