#ifndef CLIPBOARD_H
#define CLIPBOARD_H

#include <stdbool.h>

// Callback sẽ được gọi khi clipboard trên máy tính thay đổi
typedef void (*sc_clipboard_callback)(const char *text, void *userdata);

struct sc_clipboard_monitor;

// Khởi tạo bộ theo dõi clipboard
struct sc_clipboard_monitor *sc_clipboard_monitor_new(sc_clipboard_callback cb, void *userdata);

// Bắt đầu theo dõi
bool sc_clipboard_monitor_start(struct sc_clipboard_monitor *monitor);

// Dừng theo dõi
void sc_clipboard_monitor_stop(struct sc_clipboard_monitor *monitor);

// Hủy và giải phóng bộ nhớ
void sc_clipboard_monitor_destroy(struct sc_clipboard_monitor *monitor);

// Hàm để cập nhật clipboard của máy tính (sẽ được gọi khi nhận được từ thiết bị)
bool sc_clipboard_set_text(const char *text);


#endif 