#include "log.h"
#include <stdarg.h>
#include <string.h>

// Global log level
log_level_t g_log_level = LOG_LEVEL_INFO;

// Static buffer cho Windows error messages
static char error_buffer[256];

void log_with_timestamp(log_level_t level, const char *file, int line, const char *format, ...) {
    // Get current time
    SYSTEMTIME st;
    GetLocalTime(&st);
    
    // Level strings
    const char *level_str[] = {"DEBUG", "INFO", "WARN", "ERROR"};
    
    // Extract filename from full path
    const char *filename = strrchr(file, '\\');
    if (!filename) filename = strrchr(file, '/');
    if (!filename) filename = file;
    else filename++; // Skip the slash
    
    // Print timestamp and level
    printf("[%02d:%02d:%02d.%03d] [%s] [%s:%d] ", 
           st.wHour, st.wMinute, st.wSecond, st.wMilliseconds,
           level_str[level], filename, line);
    
    // Print the actual message
    va_list args;
    va_start(args, format);
    vprintf(format, args);
    va_end(args);
    
    printf("\n");
    fflush(stdout);
}

void log_set_level(log_level_t level) {
    g_log_level = level;
}

const char* get_windows_error_message(DWORD error_code) {
    DWORD result = FormatMessageA(
        FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL,
        error_code,
        MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        error_buffer,
        sizeof(error_buffer) - 1,
        NULL
    );
    
    if (result == 0) {
        snprintf(error_buffer, sizeof(error_buffer), "Unknown error (code %lu)", error_code);
    } else {
        // Remove trailing newline if present
        size_t len = strlen(error_buffer);
        if (len > 0 && error_buffer[len-1] == '\n') {
            error_buffer[len-1] = '\0';
            if (len > 1 && error_buffer[len-2] == '\r') {
                error_buffer[len-2] = '\0';
            }
        }
    }
    
    return error_buffer;
}
