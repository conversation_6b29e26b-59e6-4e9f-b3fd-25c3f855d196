package com.clipsyncadb.wrappers;

import android.os.IBinder;
import android.os.IInterface;

public interface IOnPrimaryClipChangedListener extends IInterface {
    void dispatchPrimaryClipChanged();

    // This is the trick to convert a binder to a listener interface
    abstract class Stub {
        public static IOnPrimaryClipChangedListener asInterface(IBinder binder) {
            // A real implementation would be much more complex.
            // For our purpose, a simple proxy is enough to receive the callback.
            // The method signature is the most important part for reflection.
            return (IOnPrimaryClipChangedListener) java.lang.reflect.Proxy.newProxyInstance(
                Stub.class.getClassLoader(),
                new Class<?>[] { IOnPrimaryClipChangedListener.class },
                (proxy, method, args) -> {
                    // We only have one method, so we don't need to check the name.
                    // This is where we would call the actual implementation if we had one.
                    return null;
                }
            );
        }
    }
} 