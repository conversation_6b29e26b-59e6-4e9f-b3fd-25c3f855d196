#ifndef SOCKETS_H
#define SOCKETS_H

#include <stdint.h>
#include <stdbool.h>

#ifdef _WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#define SC_SOCKET_T SOCKET
#define SC_INVALID_SOCKET INVALID_SOCKET
#else
#define SC_SOCKET_T int
#define SC_INVALID_SOCKET -1
#endif

// Khởi tạo network (cần cho Winsock trên Windows)
bool net_init(void);

// Kết nối tới một địa chỉ và cổng
SC_SOCKET_T net_connect(uint32_t ip, uint16_t port);

// Đóng một socket
bool net_close(SC_SOCKET_T socket);

// Gửi một buffer
int net_send_all(SC_SOCKET_T socket, const void *buf, size_t len);

// Nhận một buffer
int net_recv_all(SC_SOCKET_T socket, void *buf, size_t len);

#endif 