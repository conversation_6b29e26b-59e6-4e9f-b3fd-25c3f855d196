#include "sockets.h"

#include <stdio.h>
#include <stdbool.h>
#include <windows.h> // for Sleep
#include <conio.h>   // for _kbhit, _getch

#include "adb.h"
#include "communication.h"
#include "log.h"

#define SERVER_JAR_PATH "../../server/build/libs/clipsync-server-1.0.jar"
#define SERVER_REMOTE_PATH "/data/local/tmp/clipsync-server.jar"
#define SOCKET_NAME "clipsyncadb"
#define LOCAL_PORT 27189

// Global variables cho reconnection
static char g_serial[SC_ADB_SERIAL_MAX_LENGTH];
static struct sc_communication_context *g_comm_ctx = NULL;
static volatile bool g_should_reconnect = false;


static bool push_server_and_run(const char *serial) {
    LOGI("Day server len thiet bi...");
    if (!sc_adb_push(serial, SERVER_JAR_PATH, SERVER_REMOTE_PATH)) {
        LOGE("Khong the day server jar");
        return false;
    }
    LOGI("Da day server thanh cong");

    LOGI("Chay server tren thiet bi...");
    const char *const shell_args[] = {
        "CLASSPATH=" SERVER_REMOTE_PATH,
        "app_process",
        "/",
        "com.clipsyncadb.Server",
        NULL
    };
    SC_PID_T pid = sc_adb_execute_shell_async(serial, shell_args);
    if (pid == INVALID_HANDLE_VALUE) {
        LOGE("Khong the chay server tren thiet bi");
        return false;
    }
    // Không cần chờ, server sẽ chạy ở background
    LOGI("Da gui lenh khoi dong server");

    return true;
}

static SC_SOCKET_T connect_to_server(const char *serial) {
    LOGI("Thiet lap ket noi toi server...");

    if (!sc_adb_forward(serial, LOCAL_PORT, SOCKET_NAME)) {
        LOGE("Khong the forward cong %d", LOCAL_PORT);
        return SC_INVALID_SOCKET;
    }
    LOGI("Da forward cong %d", LOCAL_PORT);

    // Chờ một chút để server có thời gian khởi động
    LOGI("Cho server khoi dong...");
    Sleep(2000);

    LOGI("Dang ket noi toi server...");
    // Sử dụng retry logic như scrcpy-master: 50 attempts, 200ms delay
    SC_SOCKET_T socket = net_connect_with_retry(0x7F000001, LOCAL_PORT, 50, 200);
    if (socket == SC_INVALID_SOCKET) {
        LOGE("Khong the ket noi toi socket tren thiet bi sau nhieu lan thu");
        sc_adb_forward_remove(serial, LOCAL_PORT);
        return SC_INVALID_SOCKET;
    }

    LOGI("Da ket noi toi server qua socket");

    // Thêm bước gửi dummy byte để báo cho server biết client đã sẵn sàng
    // Đây là một cơ chế đồng bộ hóa đơn giản mà scrcpy gốc sử dụng
    char dummy_byte = 0;
    if (net_send_all(socket, &dummy_byte, 1) < 0) {
        LOGE("Khong the gui dummy byte toi server");
        net_close(socket);
        sc_adb_forward_remove(serial, LOCAL_PORT);
        return SC_INVALID_SOCKET;
    }
    LOGI("Da gui tin hieu san sang cho server");

    return socket;
}

// Callback được gọi khi connection bị disconnect
static void on_disconnect(void *userdata) {
    LOGW("Phat hien mat ket noi!");
    g_should_reconnect = true;
}

// Function để thực hiện reconnection
static bool attempt_reconnect(void) {
    LOGI("Dang thu reconnect...");

    // Thử kết nối lại
    SC_SOCKET_T new_socket = connect_to_server(g_serial);
    if (new_socket == SC_INVALID_SOCKET) {
        LOGE("Reconnect that bai");
        return false;
    }

    // Reconnect communication context
    if (!sc_communication_reconnect(g_comm_ctx, new_socket)) {
        LOGE("Khong the reconnect communication context");
        net_close(new_socket);
        return false;
    }

    LOGI("Reconnect thanh cong!");
    g_should_reconnect = false;
    return true;
}

int main(int argc, char *argv[]) {
    // Set log level (có thể thay đổi để debug)
    log_set_level(LOG_LEVEL_INFO);

    LOGI("Khoi dong ClipSyncADB Client...");

    if (!sc_adb_init()) {
        LOGE("Khong the khoi tao adb");
        return 1;
    }

    if (!net_init()) {
        LOGE("Khong the khoi tao network (Winsock)");
        return 1;
    }

    if (argc > 1) {
        strncpy(g_serial, argv[1], SC_ADB_SERIAL_MAX_LENGTH - 1);
        g_serial[SC_ADB_SERIAL_MAX_LENGTH - 1] = '\0';
    } else {
        if (!sc_adb_get_first_device_serial(g_serial, sizeof(g_serial))) {
             fprintf(stderr, "Loi: Khong tim thay thiet bi nao. Cung cap serial qua tham so dong lenh: .\\clipsyncadb-client.exe <serial>\n");
             return 1;
        }
    }
    printf("Su dung thiet bi: %s\n", g_serial);
    
    // NOTE: Cần build server trước để có file jar
    if (!push_server_and_run(g_serial)) {
        return 1;
    }

    SC_SOCKET_T socket = connect_to_server(g_serial);
    if (socket == SC_INVALID_SOCKET) {
        return 1;
    }

    printf("4. Bat dau dong bo clipboard.\n");

    g_comm_ctx = sc_communication_new(socket);
    if (!g_comm_ctx || !sc_communication_start(g_comm_ctx)) {
        fprintf(stderr, "Loi: Khong the bat dau cac luong giao tiep.\n");
        net_close(socket);
        sc_adb_forward_remove(g_serial, LOCAL_PORT);
        return 1;
    }

    // Set disconnect callback
    sc_communication_set_disconnect_callback(g_comm_ctx, on_disconnect, NULL);

    printf("\n*** Dong bo Clipboard dang chay. Nhan Enter de thoat. ***\n");
    printf("*** Chuong trinh se tu dong reconnect khi mat ket noi. ***\n");

    // Main loop với reconnection logic
    while (true) {
        if (g_should_reconnect) {
            printf("\nThu reconnect trong 3 giay...\n");
            Sleep(3000);

            if (!attempt_reconnect()) {
                printf("Reconnect that bai. Thu lai sau 10 giay...\n");
                Sleep(10000);
                continue;
            }
        }

        // Check for user input (non-blocking)
        if (_kbhit()) {
            char c = _getch();
            if (c == '\r' || c == '\n') {
                break;
            }
        }

        Sleep(100); // Tránh busy waiting
    }

    printf("Dang dung ClipSyncADB Client...\n");

    sc_communication_stop(g_comm_ctx);
    sc_communication_destroy(g_comm_ctx);
    sc_adb_forward_remove(g_serial, LOCAL_PORT);

    printf("Da don dep va thoat.\n");

    return 0;
} 