#include "sockets.h"

#include <stdio.h>
#include <stdbool.h>
#include <windows.h> // for Sleep

#include "adb.h"
#include "communication.h"

#define SERVER_JAR_PATH "../../server/build/libs/clipsync-server-1.0.jar"
#define SERVER_REMOTE_PATH "/data/local/tmp/clipsync-server.jar"
#define SOCKET_NAME "clipsyncadb"
#define LOCAL_PORT 27189


static bool push_server_and_run(const char *serial) {
    printf("1. Day server len thiet bi...\n");
    if (!sc_adb_push(serial, SERVER_JAR_PATH, SERVER_REMOTE_PATH)) {
        fprintf(stderr, "Loi: Khong the day server jar.\n");
        return false;
    }
    printf("   -> Da day server.\n");

    printf("2. Chay server tren thiet bi...\n");
    const char *const shell_args[] = {
        "CLASSPATH=" SERVER_REMOTE_PATH,
        "app_process",
        "/",
        "com.clipsyncadb.Server",
        NULL
    };
    SC_PID_T pid = sc_adb_execute_shell_async(serial, shell_args);
    if (pid == INVALID_HANDLE_VALUE) {
        fprintf(stderr, "Loi: Khong the chay server tren thiet bi.\n");
        return false;
    }
    // Không cần chờ, server sẽ chạy ở background
    printf("   -> Da gui lenh khoi dong server.\n");

    return true;
}

static SC_SOCKET_T connect_to_server(const char *serial) {
    printf("3. Thiet lap ket noi toi server...\n");

    // Chờ một chút để server có thời gian khởi động
    Sleep(1000); 

    if (!sc_adb_forward(serial, LOCAL_PORT, SOCKET_NAME)) {
        fprintf(stderr, "Loi: Khong the forward cong.\n");
        return SC_INVALID_SOCKET;
    }
    printf("   -> Da forward cong %d.\n", LOCAL_PORT);

    printf("   -> Dang ket noi toi server...\n");
    SC_SOCKET_T socket = net_connect(0x7F000001, LOCAL_PORT); // 127.0.0.1
    if (socket == SC_INVALID_SOCKET) {
        fprintf(stderr, "Loi: Khong the ket noi toi socket tren thiet bi.\n");
        sc_adb_forward_remove(serial, LOCAL_PORT);
        return SC_INVALID_SOCKET;
    }
    
    printf("   -> Da ket noi toi server qua socket.\n");

    // Thêm bước gửi dummy byte để báo cho server biết client đã sẵn sàng
    // Đây là một cơ chế đồng bộ hóa đơn giản mà scrcpy gốc sử dụng
    char dummy_byte = 0;
    if (net_send_all(socket, &dummy_byte, 1) < 0) {
        fprintf(stderr, "Loi: Khong the gui dummy byte toi server.\n");
        net_close(socket);
        sc_adb_forward_remove(serial, LOCAL_PORT);
        return SC_INVALID_SOCKET;
    }
    printf("   -> Da gui tin hieu san sang cho server.\n");

    return socket;
}

int main(int argc, char *argv[]) {
    printf("Khoi dong ClipSyncADB Client...\n");

    if (!sc_adb_init()) {
        fprintf(stderr, "Loi: Khong the khoi tao adb.\n");
        return 1;
    }

    if (!net_init()) {
        fprintf(stderr, "Loi: Khong the khoi tao network (Winsock).\n");
        return 1;
    }

    char serial[SC_ADB_SERIAL_MAX_LENGTH];
    if (argc > 1) {
        strncpy(serial, argv[1], SC_ADB_SERIAL_MAX_LENGTH - 1);
        serial[SC_ADB_SERIAL_MAX_LENGTH - 1] = '\0';
    } else {
        if (!sc_adb_get_first_device_serial(serial, sizeof(serial))) {
             fprintf(stderr, "Loi: Khong tim thay thiet bi nao. Cung cap serial qua tham so dong lenh: .\\clipsyncadb-client.exe <serial>\n");
             return 1;
        }
    }
    printf("Su dung thiet bi: %s\n", serial);
    
    // NOTE: Cần build server trước để có file jar
    if (!push_server_and_run(serial)) {
        return 1;
    }

    SC_SOCKET_T socket = connect_to_server(serial);
    if (socket == SC_INVALID_SOCKET) {
        return 1;
    }

    printf("4. Bat dau dong bo clipboard.\n");
    
    struct sc_communication_context *comm_ctx = sc_communication_new(socket);
    if (!comm_ctx || !sc_communication_start(comm_ctx)) {
        fprintf(stderr, "Loi: Khong the bat dau cac luong giao tiep.\n");
        net_close(socket);
        sc_adb_forward_remove(serial, LOCAL_PORT);
        return 1;
    }

    printf("\n*** Dong bo Clipboard dang chay. Nhan Enter de thoat. ***\n");
    getchar();

    printf("Dang dung ClipSyncADB Client...\n");

    sc_communication_stop(comm_ctx);
    sc_communication_destroy(comm_ctx);
    sc_adb_forward_remove(serial, LOCAL_PORT);

    printf("Da don dep va thoat.\n");

    return 0;
} 