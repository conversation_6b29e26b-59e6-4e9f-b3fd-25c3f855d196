# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = C:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "No interactive CMake dialog available..."
	C:\msys64\mingw64\bin\cmake.exe -E echo "No interactive CMake dialog available."
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	C:\msys64\mingw64\bin\cmake.exe --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\CMakeFiles C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named clipsyncadb-client

# Build rule for target.
clipsyncadb-client: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clipsyncadb-client
.PHONY : clipsyncadb-client

# fast build rule for target.
clipsyncadb-client/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/build
.PHONY : clipsyncadb-client/fast

adb.obj: adb.c.obj
.PHONY : adb.obj

# target to build an object file
adb.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/adb.c.obj
.PHONY : adb.c.obj

adb.i: adb.c.i
.PHONY : adb.i

# target to preprocess a source file
adb.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/adb.c.i
.PHONY : adb.c.i

adb.s: adb.c.s
.PHONY : adb.s

# target to generate assembly for a file
adb.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/adb.c.s
.PHONY : adb.c.s

clipboard.obj: clipboard.c.obj
.PHONY : clipboard.obj

# target to build an object file
clipboard.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/clipboard.c.obj
.PHONY : clipboard.c.obj

clipboard.i: clipboard.c.i
.PHONY : clipboard.i

# target to preprocess a source file
clipboard.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/clipboard.c.i
.PHONY : clipboard.c.i

clipboard.s: clipboard.c.s
.PHONY : clipboard.s

# target to generate assembly for a file
clipboard.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/clipboard.c.s
.PHONY : clipboard.c.s

communication.obj: communication.c.obj
.PHONY : communication.obj

# target to build an object file
communication.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/communication.c.obj
.PHONY : communication.c.obj

communication.i: communication.c.i
.PHONY : communication.i

# target to preprocess a source file
communication.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/communication.c.i
.PHONY : communication.c.i

communication.s: communication.c.s
.PHONY : communication.s

# target to generate assembly for a file
communication.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/communication.c.s
.PHONY : communication.c.s

main.obj: main.c.obj
.PHONY : main.obj

# target to build an object file
main.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/main.c.obj
.PHONY : main.c.obj

main.i: main.c.i
.PHONY : main.i

# target to preprocess a source file
main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/main.c.i
.PHONY : main.c.i

main.s: main.c.s
.PHONY : main.s

# target to generate assembly for a file
main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/main.c.s
.PHONY : main.c.s

sockets.obj: sockets.c.obj
.PHONY : sockets.obj

# target to build an object file
sockets.c.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/sockets.c.obj
.PHONY : sockets.c.obj

sockets.i: sockets.c.i
.PHONY : sockets.i

# target to preprocess a source file
sockets.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/sockets.c.i
.PHONY : sockets.c.i

sockets.s: sockets.c.s
.PHONY : sockets.s

# target to generate assembly for a file
sockets.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/sockets.c.s
.PHONY : sockets.c.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... clipsyncadb-client
	@echo ... adb.obj
	@echo ... adb.i
	@echo ... adb.s
	@echo ... clipboard.obj
	@echo ... clipboard.i
	@echo ... clipboard.s
	@echo ... communication.obj
	@echo ... communication.i
	@echo ... communication.s
	@echo ... main.obj
	@echo ... main.i
	@echo ... main.s
	@echo ... sockets.obj
	@echo ... sockets.i
	@echo ... sockets.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

