#include "communication.h"
#include "clipboard.h"
#include "log.h"
#include <windows.h> // For threads
#include <stdio.h>

#define CLIPBOARD_TEXT_MAX_LEN (1 << 18) // 256k

struct sc_communication_context {
    SC_SOCKET_T socket;
    HANDLE receiver_thread;
    struct sc_clipboard_monitor *monitor;
    volatile bool stopped;
    volatile bool connection_alive;

    // Callback cho disconnection event
    sc_connection_callback disconnect_callback;
    void *disconnect_userdata;

    // Mutex để protect socket operations
    CRITICAL_SECTION socket_mutex;
};

/**
 * Gửi text tới server trên thiết bị với thread-safe socket access.
 */
static bool send_clipboard_text_safe(struct sc_communication_context *ctx, const char* text) {
    if (!text || !ctx->connection_alive) {
        return true;
    }

    size_t len = strlen(text);
    if (len > CLIPBOARD_TEXT_MAX_LEN) {
        fprintf(stderr, "WARN: Van ban clipboard qua dai, khong gui.\n");
        return false;
    }

    // Protocol: [1 byte: type] + [4 bytes: length] + [length bytes: text]
    unsigned char header[5];
    header[0] = 0; // TYPE_SET_CLIPBOARD

    // a simple Big Endian conversion
    header[1] = (len >> 24) & 0xFF;
    header[2] = (len >> 16) & 0xFF;
    header[3] = (len >> 8) & 0xFF;
    header[4] = len & 0xFF;

    EnterCriticalSection(&ctx->socket_mutex);

    bool success = true;
    if (ctx->connection_alive) {
        if (net_send_all(ctx->socket, header, 5) < 0) {
            success = false;
            ctx->connection_alive = false;
        } else if (net_send_all(ctx->socket, text, len) < 0) {
            success = false;
            ctx->connection_alive = false;
        }
    } else {
        success = false;
    }

    LeaveCriticalSection(&ctx->socket_mutex);

    if (!success && ctx->disconnect_callback) {
        ctx->disconnect_callback(ctx->disconnect_userdata);
    }

    return success;
}

/**
 * Callback được gọi bởi clipboard monitor khi clipboard máy tính thay đổi.
 */
static void on_new_clipboard_text(const char *text, void *userdata) {
    struct sc_communication_context *ctx = userdata;
    LOGI("Gui clipboard toi thiet bi (len: %zu)", text ? strlen(text) : 0);
    if (!send_clipboard_text_safe(ctx, text)) {
        LOGE("Khong the gui clipboard toi thiet bi");
        // Connection sẽ được handle bởi disconnect callback
    }
}


/**
 * Luồng này nhận dữ liệu từ server và cập nhật clipboard trên máy tính.
 */
static DWORD WINAPI run_receiver_thread(LPVOID param) {
    struct sc_communication_context *ctx = param;
    char buffer[CLIPBOARD_TEXT_MAX_LEN + 1];

    while (!ctx->stopped && ctx->connection_alive) {
        unsigned char header[5];

        EnterCriticalSection(&ctx->socket_mutex);
        int recv_result = 0;
        if (ctx->connection_alive) {
            recv_result = net_recv_all(ctx->socket, header, 5);
        } else {
            recv_result = -1; // Force break
        }
        LeaveCriticalSection(&ctx->socket_mutex);

        if (recv_result != 0) {
            if (!ctx->stopped) {
                LOGE("Mat ket noi toi server khi doc header");
                ctx->connection_alive = false;
                if (ctx->disconnect_callback) {
                    ctx->disconnect_callback(ctx->disconnect_userdata);
                }
            }
            break;
        }

        // type is header[0]
        size_t len = (header[1] << 24) | (header[2] << 16) | (header[3] << 8) | header[4];

        if (len > CLIPBOARD_TEXT_MAX_LEN) {
            LOGE("Goi tin clipboard nhan duoc qua lon: %zu bytes", len);
            ctx->connection_alive = false;
            if (ctx->disconnect_callback) {
                ctx->disconnect_callback(ctx->disconnect_userdata);
            }
            break;
        }

        if (len > 0) {
            EnterCriticalSection(&ctx->socket_mutex);
            recv_result = 0;
            if (ctx->connection_alive) {
                recv_result = net_recv_all(ctx->socket, buffer, len);
            } else {
                recv_result = -1;
            }
            LeaveCriticalSection(&ctx->socket_mutex);

            if (recv_result != 0) {
                 if (!ctx->stopped) {
                    LOGE("Mat ket noi khi dang doc clipboard data");
                    ctx->connection_alive = false;
                    if (ctx->disconnect_callback) {
                        ctx->disconnect_callback(ctx->disconnect_userdata);
                    }
                }
                break;
            }
        }
        buffer[len] = '\0';

        LOGI("Nhan clipboard tu thiet bi (len: %zu)", len);
        sc_clipboard_set_text(buffer);
    }

    // Báo cho luồng chính là đã đến lúc dừng lại
    LOGI("Luong nhan da dung");

    return 0;
}


struct sc_communication_context* sc_communication_new(SC_SOCKET_T socket) {
    struct sc_communication_context *ctx = malloc(sizeof(*ctx));
    if (!ctx) return NULL;

    ctx->socket = socket;
    ctx->stopped = false;
    ctx->connection_alive = true;
    ctx->disconnect_callback = NULL;
    ctx->disconnect_userdata = NULL;

    InitializeCriticalSection(&ctx->socket_mutex);

    ctx->monitor = sc_clipboard_monitor_new(on_new_clipboard_text, ctx);
    if (!ctx->monitor) {
        DeleteCriticalSection(&ctx->socket_mutex);
        free(ctx);
        return NULL;
    }
    return ctx;
}

bool sc_communication_start(struct sc_communication_context *ctx) {
    if (!sc_clipboard_monitor_start(ctx->monitor)) {
        return false;
    }

    ctx->receiver_thread = CreateThread(NULL, 0, run_receiver_thread, ctx, 0, NULL);
    if (!ctx->receiver_thread) {
        sc_clipboard_monitor_stop(ctx->monitor);
        return false;
    }
    
    LOGI("Da bat dau theo doi va dong bo clipboard");
    return true;
}

void sc_communication_stop(struct sc_communication_context *ctx) {
    if (ctx) {
        ctx->stopped = true;
        ctx->connection_alive = false;
        sc_clipboard_monitor_stop(ctx->monitor);

        EnterCriticalSection(&ctx->socket_mutex);
        net_close(ctx->socket); // This will unblock the receiver thread
        LeaveCriticalSection(&ctx->socket_mutex);

        if (ctx->receiver_thread) {
            WaitForSingleObject(ctx->receiver_thread, INFINITE);
        }
    }
}

void sc_communication_destroy(struct sc_communication_context *ctx) {
    if (ctx) {
        sc_clipboard_monitor_destroy(ctx->monitor);
        if (ctx->receiver_thread) {
            CloseHandle(ctx->receiver_thread);
        }
        DeleteCriticalSection(&ctx->socket_mutex);
        free(ctx);
    }
}

// Implement các function mới
void sc_communication_set_disconnect_callback(struct sc_communication_context *ctx,
                                            sc_connection_callback callback, void *userdata) {
    if (ctx) {
        ctx->disconnect_callback = callback;
        ctx->disconnect_userdata = userdata;
    }
}

bool sc_communication_reconnect(struct sc_communication_context *ctx, SC_SOCKET_T new_socket) {
    if (!ctx) return false;

    // Dừng receiver thread cũ
    ctx->stopped = true;
    ctx->connection_alive = false;

    EnterCriticalSection(&ctx->socket_mutex);
    net_close(ctx->socket);
    ctx->socket = new_socket;
    ctx->connection_alive = true;
    ctx->stopped = false;
    LeaveCriticalSection(&ctx->socket_mutex);

    // Đóng thread cũ
    if (ctx->receiver_thread) {
        WaitForSingleObject(ctx->receiver_thread, INFINITE);
        CloseHandle(ctx->receiver_thread);
    }

    // Tạo receiver thread mới
    ctx->receiver_thread = CreateThread(NULL, 0, run_receiver_thread, ctx, 0, NULL);
    if (!ctx->receiver_thread) {
        ctx->connection_alive = false;
        return false;
    }

    LOGI("Da reconnect thanh cong");
    return true;
}

bool sc_communication_is_alive(struct sc_communication_context *ctx) {
    return ctx && ctx->connection_alive && !ctx->stopped;
}