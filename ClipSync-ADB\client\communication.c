#include "communication.h"
#include "clipboard.h"
#include <windows.h> // For threads
#include <stdio.h>

#define CLIPBOARD_TEXT_MAX_LEN (1 << 18) // 256k

struct sc_communication_context {
    SC_SOCKET_T socket;
    HANDLE receiver_thread;
    struct sc_clipboard_monitor *monitor;
    volatile bool stopped;
};

/**
 * Gửi text tới server trên thiết bị.
 */
static bool send_clipboard_text(SC_SOCKET_T socket, const char* text) {
    if (!text) {
        return true;
    }
    size_t len = strlen(text);
    if (len > CLIPBOARD_TEXT_MAX_LEN) {
        fprintf(stderr, "WARN: Van ban clipboard qua dai, khong gui.\n");
        return false;
    }

    // Protocol: [1 byte: type] + [4 bytes: length] + [length bytes: text]
    unsigned char header[5];
    header[0] = 0; // TYPE_SET_CLIPBOARD
    
    // a simple Big Endian conversion
    header[1] = (len >> 24) & 0xFF;
    header[2] = (len >> 16) & 0xFF;
    header[3] = (len >> 8) & 0xFF;
    header[4] = len & 0xFF;

    if (net_send_all(socket, header, 5) < 0) {
        return false;
    }
    if (net_send_all(socket, text, len) < 0) {
        return false;
    }
    return true;
}

/**
 * Callback được gọi bởi clipboard monitor khi clipboard máy tính thay đổi.
 */
static void on_new_clipboard_text(const char *text, void *userdata) {
    struct sc_communication_context *ctx = userdata;
    printf("-> Gui clipboard toi thiet bi...\n");
    if (!send_clipboard_text(ctx->socket, text)) {
        fprintf(stderr, "Loi: Khong the gui clipboard toi thiet bi.\n");
        // có thể dừng chương trình ở đây
    }
}


/**
 * Luồng này nhận dữ liệu từ server và cập nhật clipboard trên máy tính.
 */
static DWORD WINAPI run_receiver_thread(LPVOID param) {
    struct sc_communication_context *ctx = param;
    char buffer[CLIPBOARD_TEXT_MAX_LEN + 1];

    while (!ctx->stopped) {
        unsigned char header[5];
        if (net_recv_all(ctx->socket, header, 5) != 0) {
            if (!ctx->stopped) {
                fprintf(stderr, "Loi: Mat ket noi toi server.\n");
            }
            break;
        }

        // type is header[0]
        size_t len = (header[1] << 24) | (header[2] << 16) | (header[3] << 8) | header[4];

        if (len > CLIPBOARD_TEXT_MAX_LEN) {
            fprintf(stderr, "Loi: Goi tin clipboard nhan duoc qua lon.\n");
            break;
        }

        if (len > 0) {
            if (net_recv_all(ctx->socket, buffer, len) != 0) {
                 if (!ctx->stopped) {
                    fprintf(stderr, "Loi: Mat ket noi khi dang doc clipboard.\n");
                }
                break;
            }
        }
        buffer[len] = '\0';

        printf("<- Nhan clipboard tu thiet bi.\n");
        sc_clipboard_set_text(buffer);
    }
    
    // Báo cho luồng chính là đã đến lúc dừng lại
    // (Trong một ứng dụng thực tế có thể dùng event hoặc callback)
    printf("Luong nhan da dung. Nhan Enter de thoat.\n");

    return 0;
}


struct sc_communication_context* sc_communication_new(SC_SOCKET_T socket) {
    struct sc_communication_context *ctx = malloc(sizeof(*ctx));
    if (!ctx) return NULL;

    ctx->socket = socket;
    ctx->stopped = false;
    ctx->monitor = sc_clipboard_monitor_new(on_new_clipboard_text, ctx);
    if (!ctx->monitor) {
        free(ctx);
        return NULL;
    }
    return ctx;
}

bool sc_communication_start(struct sc_communication_context *ctx) {
    if (!sc_clipboard_monitor_start(ctx->monitor)) {
        return false;
    }

    ctx->receiver_thread = CreateThread(NULL, 0, run_receiver_thread, ctx, 0, NULL);
    if (!ctx->receiver_thread) {
        sc_clipboard_monitor_stop(ctx->monitor);
        return false;
    }
    
    printf("Da bat dau theo doi va dong bo clipboard.\n");
    return true;
}

void sc_communication_stop(struct sc_communication_context *ctx) {
    if (ctx) {
        ctx->stopped = true;
        sc_clipboard_monitor_stop(ctx->monitor);
        net_close(ctx->socket); // This will unblock the receiver thread
        if (ctx->receiver_thread) {
            WaitForSingleObject(ctx->receiver_thread, INFINITE);
        }
    }
}

void sc_communication_destroy(struct sc_communication_context *ctx) {
    if (ctx) {
        sc_clipboard_monitor_destroy(ctx->monitor);
        if (ctx->receiver_thread) {
            CloseHandle(ctx->receiver_thread);
        }
        free(ctx);
    }
} 