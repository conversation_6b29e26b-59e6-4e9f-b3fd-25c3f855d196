package com.clipsyncadb;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

public final class ControlMessage {

    public static final int TYPE_SET_CLIPBOARD = 0;

    private final int type;
    private final String text;

    private ControlMessage(int type, String text) {
        this.type = type;
        this.text = text;
    }

    public static ControlMessage createSetClipboard(String text) {
        return new ControlMessage(TYPE_SET_CLIPBOARD, text);
    }

    public void sendTo(OutputStream stream) throws IOException {
        byte[] buffer;
        if (text != null) {
            byte[] textBytes = text.getBytes(StandardCharsets.UTF_8);
            buffer = new byte[1 + 4 + textBytes.length];
            buffer[0] = (byte) type;
            ByteBuffer.wrap(buffer, 1, 4).putInt(textBytes.length);
            System.arraycopy(textBytes, 0, buffer, 5, textBytes.length);
        } else {
            // Gửi text rỗng
            buffer = new byte[1 + 4];
            buffer[0] = (byte) type;
            ByteBuffer.wrap(buffer, 1, 4).putInt(0);
        }
        stream.write(buffer);
    }
} 