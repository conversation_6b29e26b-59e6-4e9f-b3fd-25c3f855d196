cmake_minimum_required(VERSION 3.10)
project(ClipSyncADBClient C)

set(CMAKE_C_STANDARD 11)

# Thêm source files vào đây
add_executable(clipsyncadb-client
    main.c
    adb.c
    sockets.c
    clipboard.c
    communication.c
    log.c
)

# Thê<PERSON> các thư viện cần thiết (ví dụ: ws2_32 cho sockets trên Windows)
if(WIN32)
    target_link_libraries(clipsyncadb-client ws2_32)
endif()

# Thêm hướng dẫn build vào đây
# Ví dụ:
# mkdir build
# cd build
# cmake .. -G "MinGW Makefiles"
# make 