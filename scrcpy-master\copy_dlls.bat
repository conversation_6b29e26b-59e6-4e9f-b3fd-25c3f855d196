@echo off
REM Copy các DLL cần thiết từ MINGW64 tới thư mục scrcpy

echo ========================================
echo Copy DLL Files cho Scrcpy
echo ========================================
echo.

REM Kiểm tra thư mục MINGW64 có tồn tại không
if not exist "C:\msys64\mingw64\bin" (
    echo ERROR: Không tìm thấy MINGW64
    echo Vui lòng cài đặt MSYS2/MINGW64 trước
    pause
    exit /b 1
)

REM Kiểm tra thư mục build có tồn tại không
if not exist "build-win64-native\app" (
    echo ERROR: Không tìm thấy thư mục build-win64-native\app
    echo Vui lòng build scrcpy trước khi chạy script này
    pause
    exit /b 1
)

echo Đang copy các DLL files...
echo.

REM Copy FFmpeg DLLs
echo Copying FFmpeg libraries...
copy "C:\msys64\mingw64\bin\avcodec-61.dll" "build-win64-native\app\" >nul 2>&1
if %errorlevel% equ 0 echo ✅ avcodec-61.dll
copy "C:\msys64\mingw64\bin\avutil-59.dll" "build-win64-native\app\" >nul 2>&1
if %errorlevel% equ 0 echo ✅ avutil-59.dll
copy "C:\msys64\mingw64\bin\avformat-61.dll" "build-win64-native\app\" >nul 2>&1
if %errorlevel% equ 0 echo ✅ avformat-61.dll
copy "C:\msys64\mingw64\bin\swresample-5.dll" "build-win64-native\app\" >nul 2>&1
if %errorlevel% equ 0 echo ✅ swresample-5.dll

REM Copy SDL2 DLL
echo.
echo Copying SDL2 library...
copy "C:\msys64\mingw64\bin\SDL2.dll" "build-win64-native\app\" >nul 2>&1
if %errorlevel% equ 0 echo ✅ SDL2.dll

REM Copy USB DLL
echo.
echo Copying USB library...
copy "C:\msys64\mingw64\bin\libusb-1.0.dll" "build-win64-native\app\" >nul 2>&1
if %errorlevel% equ 0 echo ✅ libusb-1.0.dll

REM Copy Runtime DLLs
echo.
echo Copying runtime libraries...
copy "C:\msys64\mingw64\bin\libwinpthread-1.dll" "build-win64-native\app\" >nul 2>&1
if %errorlevel% equ 0 echo ✅ libwinpthread-1.dll
copy "C:\msys64\mingw64\bin\libgcc_s_seh-1.dll" "build-win64-native\app\" >nul 2>&1
if %errorlevel% equ 0 echo ✅ libgcc_s_seh-1.dll

REM Copy thêm các DLL có thể cần thiết
echo.
echo Copying additional libraries...
copy "C:\msys64\mingw64\bin\libstdc++-6.dll" "build-win64-native\app\" >nul 2>&1
if %errorlevel% equ 0 echo ✅ libstdc++-6.dll
copy "C:\msys64\mingw64\bin\zlib1.dll" "build-win64-native\app\" >nul 2>&1
if %errorlevel% equ 0 echo ✅ zlib1.dll

echo.
echo ========================================
echo ✅ Hoàn thành copy DLL files!
echo ========================================
echo.
echo Bây giờ bạn có thể chạy scrcpy mà không cần thêm PATH:
echo   clipboard_only.bat
echo   clipboard_background.bat
echo   clipboard_service.bat
echo.
pause
