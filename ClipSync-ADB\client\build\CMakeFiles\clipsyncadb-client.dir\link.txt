C:\msys64\mingw64\bin\cmake.exe -E rm -f CMakeFiles\clipsyncadb-client.dir/objects.a
C:\msys64\mingw64\bin\ar.exe qc CMakeFiles\clipsyncadb-client.dir/objects.a @CMakeFiles\clipsyncadb-client.dir\objects1.rsp
C:\msys64\mingw64\bin\cc.exe -Wl,--whole-archive CMakeFiles\clipsyncadb-client.dir/objects.a -Wl,--no-whole-archive -o clipsyncadb-client.exe -Wl,--out-implib,libclipsyncadb-client.dll.a -Wl,--major-image-version,0,--minor-image-version,0 @CMakeFiles\clipsyncadb-client.dir\linkLibs.rsp
