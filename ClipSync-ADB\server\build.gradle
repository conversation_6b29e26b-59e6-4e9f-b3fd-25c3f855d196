apply plugin: 'java'

sourceCompatibility = 1.8
targetCompatibility = 1.8

repositories {
    google()
    mavenCentral()
}

dependencies {
    // Cung cấp android.jar để biên dịch
    // Người dùng cần đảm bảo biến môi trường ANDROID_HOME được thiết lập chính xác
    compileOnly files(System.getenv("ANDROID_HOME") + '/platforms/android-33/android.jar')
}

// Chỉ định thư mục mã nguồn
sourceSets {
    main {
        java {
            srcDirs = ['src']
        }
    }
}


jar {
    archiveBaseName = 'clipsync-server'
    archiveVersion = '1.0'
    manifest {
        attributes 'Main-Class': 'com.clipsyncadb.Server'
    }
} 