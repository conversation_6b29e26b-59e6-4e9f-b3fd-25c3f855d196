#ifndef COMMUNICATION_H
#define COMMUNICATION_H

#include "sockets.h"
#include <stdbool.h>

struct sc_communication_context;

// Khởi tạo context giao tiếp
struct sc_communication_context* sc_communication_new(SC_SOCKET_T socket);

// Bắt đầu các luồng giao tiếp
bool sc_communication_start(struct sc_communication_context *ctx);

// Dừng và chờ các luồng kết thúc
void sc_communication_stop(struct sc_communication_context *ctx);

// Hủy và giải phóng context
void sc_communication_destroy(struct sc_communication_context *ctx);


#endif 