#ifndef COMMUNICATION_H
#define COMMUNICATION_H

#include "sockets.h"
#include <stdbool.h>

struct sc_communication_context;

// Callback được gọi khi connection bị disconnect
typedef void (*sc_connection_callback)(void *userdata);

// Khởi tạo context giao tiếp
struct sc_communication_context* sc_communication_new(SC_SOCKET_T socket);

// Bắt đầu các luồng giao tiếp
bool sc_communication_start(struct sc_communication_context *ctx);

// Dừng và chờ các luồng kết thúc
void sc_communication_stop(struct sc_communication_context *ctx);

// Hủy và giải phóng context
void sc_communication_destroy(struct sc_communication_context *ctx);

// Set callback cho disconnection event
void sc_communication_set_disconnect_callback(struct sc_communication_context *ctx,
                                            sc_connection_callback callback, void *userdata);

// Reconnect với socket mới
bool sc_communication_reconnect(struct sc_communication_context *ctx, SC_SOCKET_T new_socket);

// Kiểm tra xem connection có còn alive không
bool sc_communication_is_alive(struct sc_communication_context *ctx);

// Bật/tắt heartbeat monitoring
void sc_communication_set_heartbeat(struct sc_communication_context *ctx, bool enabled, unsigned interval_ms);


#endif 