#include "clipboard.h"
#define _WIN32_WINNT 0x0600
#include <windows.h>
#include <stdio.h>

struct sc_clipboard_monitor {
    HWND hwnd;
    HANDLE thread_handle;
    DWORD thread_id;
    sc_clipboard_callback cb;
    void *userdata;
    volatile bool stopped;
};

static const char CLASS_NAME[] = "ClipboardSyncADBClass";

static LRESULT CALLBACK clipboard_window_proc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    switch (msg) {
        case WM_CLIPBOARDUPDATE: {
            struct sc_clipboard_monitor *monitor = (struct sc_clipboard_monitor*)GetWindowLongPtr(hwnd, GWLP_USERDATA);
            if (monitor && monitor->cb) {
                if (OpenClipboard(hwnd)) {
                    HANDLE h_data = GetClipboardData(CF_UNICODETEXT);
                    if (h_data) {
                        wchar_t *wtext = GlobalLock(h_data);
                        if (wtext) {
                            int size_needed = WideCharToMultiByte(CP_UTF8, 0, wtext, -1, NULL, 0, NULL, NULL);
                            char *text = malloc(size_needed);
                            if (text) {
                                WideCharToMultiByte(CP_UTF8, 0, wtext, -1, text, size_needed, NULL, NULL);
                                monitor->cb(text, monitor->userdata);
                                free(text);
                            }
                            GlobalUnlock(h_data);
                        }
                    }
                    CloseClipboard();
                }
            }
            return 0;
        }
        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
    }
    return DefWindowProc(hwnd, msg, wParam, lParam);
}


static DWORD WINAPI clipboard_monitor_thread(LPVOID param) {
    struct sc_clipboard_monitor *monitor = (struct sc_clipboard_monitor *)param;

    WNDCLASS wc = {0};
    wc.lpfnWndProc = clipboard_window_proc;
    wc.hInstance = GetModuleHandle(NULL);
    wc.lpszClassName = CLASS_NAME;

    if (!RegisterClass(&wc)) {
        perror("RegisterClass");
        return 1;
    }

    monitor->hwnd = CreateWindowEx(0, CLASS_NAME, "Clipboard Monitor", 0, 0, 0, 0, 0, HWND_MESSAGE, NULL, NULL, NULL);
    if (!monitor->hwnd) {
        perror("CreateWindowEx");
        UnregisterClass(CLASS_NAME, GetModuleHandle(NULL));
        return 1;
    }

    // Lưu trữ con trỏ monitor để có thể truy cập trong window_proc
    SetWindowLongPtr(monitor->hwnd, GWLP_USERDATA, (LONG_PTR)monitor);

    if (!AddClipboardFormatListener(monitor->hwnd)) {
        DestroyWindow(monitor->hwnd);
        UnregisterClass(CLASS_NAME, GetModuleHandle(NULL));
        return 1;
    }

    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0) > 0) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    RemoveClipboardFormatListener(monitor->hwnd);
    DestroyWindow(monitor->hwnd);
    UnregisterClass(CLASS_NAME, GetModuleHandle(NULL));
    return 0;
}


struct sc_clipboard_monitor *sc_clipboard_monitor_new(sc_clipboard_callback cb, void *userdata) {
    struct sc_clipboard_monitor *monitor = malloc(sizeof(struct sc_clipboard_monitor));
    if (!monitor) return NULL;
    monitor->cb = cb;
    monitor->userdata = userdata;
    monitor->stopped = false;
    monitor->thread_handle = NULL;
    return monitor;
}

bool sc_clipboard_monitor_start(struct sc_clipboard_monitor *monitor) {
    monitor->thread_handle = CreateThread(NULL, 0, clipboard_monitor_thread, monitor, 0, &monitor->thread_id);
    return monitor->thread_handle != NULL;
}

void sc_clipboard_monitor_stop(struct sc_clipboard_monitor *monitor) {
    if (monitor && monitor->hwnd) {
        PostMessage(monitor->hwnd, WM_DESTROY, 0, 0);
        if (monitor->thread_handle) {
            WaitForSingleObject(monitor->thread_handle, INFINITE);
        }
    }
}

void sc_clipboard_monitor_destroy(struct sc_clipboard_monitor *monitor) {
    if (monitor) {
        if (monitor->thread_handle) {
            CloseHandle(monitor->thread_handle);
        }
        free(monitor);
    }
}

bool sc_clipboard_set_text(const char *text) {
    int w_len = MultiByteToWideChar(CP_UTF8, 0, text, -1, NULL, 0);
    HGLOBAL h_mem = GlobalAlloc(GMEM_MOVEABLE, (SIZE_T)w_len * sizeof(wchar_t));
    if (!h_mem) return false;

    wchar_t *w_text = GlobalLock(h_mem);
    if (!w_text) {
        GlobalFree(h_mem);
        return false;
    }
    MultiByteToWideChar(CP_UTF8, 0, text, -1, w_text, w_len);
    GlobalUnlock(h_mem);

    if (!OpenClipboard(NULL)) {
        GlobalFree(h_mem);
        return false;
    }
    EmptyClipboard();
    SetClipboardData(CF_UNICODETEXT, h_mem);
    CloseClipboard();
    return true;
} 