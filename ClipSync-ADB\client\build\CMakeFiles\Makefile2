# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = C:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/clipsyncadb-client.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/clipsyncadb-client.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/clipsyncadb-client.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/clipsyncadb-client.dir

# All Build rule for target.
CMakeFiles/clipsyncadb-client.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\CMakeFiles --progress-num=1,2,3,4,5,6 "Built target clipsyncadb-client"
.PHONY : CMakeFiles/clipsyncadb-client.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/clipsyncadb-client.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 CMakeFiles/clipsyncadb-client.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\CMakeFiles 0
.PHONY : CMakeFiles/clipsyncadb-client.dir/rule

# Convenience name for target.
clipsyncadb-client: CMakeFiles/clipsyncadb-client.dir/rule
.PHONY : clipsyncadb-client

# codegen rule for target.
CMakeFiles/clipsyncadb-client.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\CMakeFiles --progress-num=1,2,3,4,5,6 "Finished codegen for target clipsyncadb-client"
.PHONY : CMakeFiles/clipsyncadb-client.dir/codegen

# clean rule for target.
CMakeFiles/clipsyncadb-client.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\clipsyncadb-client.dir\build.make CMakeFiles/clipsyncadb-client.dir/clean
.PHONY : CMakeFiles/clipsyncadb-client.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

