#include "adb.h"

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <inttypes.h>

#ifdef _WIN32
#include <windows.h>
#else
#include <sys/wait.h>
#include <unistd.h>
#endif

static char *adb_path;

// --- Process execution (simplified from scrcpy) ---

#ifdef _WIN32

static bool
windows_process_execute(const char *const argv[], HANDLE *handle) {
    STARTUPINFO si;
    PROCESS_INFORMATION pi;
    memset(&si, 0, sizeof(si));
    si.cb = sizeof(si);

    // Build command line
    size_t len = 0;
    for (int i = 0; argv[i]; ++i) {
        len += strlen(argv[i]) + 3; // +3 for " " and quotes
    }
    char *cmd = malloc(len);
    if (!cmd) {
        return false;
    }

    char *p = cmd;
    for (int i = 0; argv[i]; ++i) {
        const char *arg = argv[i];
        p += sprintf(p, "\"%s\" ", arg);
    }
    // Remove trailing space
    if (p > cmd) {
        p[-1] = '\0';
    }

    if (!CreateProcess(NULL, cmd, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
        free(cmd);
        return false;
    }

    free(cmd);
    *handle = pi.hProcess;
    CloseHandle(pi.hThread);
    return true;
}

static bool
process_check_success_internal(SC_PID_T pid, const char *name, bool close) {
    if (pid == INVALID_HANDLE_VALUE) {
        fprintf(stderr, "Loi: Khong the thuc thi: %s\n", name);
        return false;
    }
    DWORD code;
    if (WaitForSingleObject(pid, INFINITE) != WAIT_OBJECT_0 || !GetExitCodeProcess(pid, &code)) {
        // failed to wait or get exit code
        code = -1; // an error occurred
    }
    if (close) {
        CloseHandle(pid);
    }
    if (code) {
        fprintf(stderr, "Loi: Lenh '%s' that bai (exit code %ld)\n", name, code);
        return false;
    }
    return true;
}

static bool
windows_process_execute_p(const char *const argv[], HANDLE *handle, HANDLE *pout) {
    STARTUPINFOA si;
    PROCESS_INFORMATION pi;
    memset(&si, 0, sizeof(si));
    si.cb = sizeof(si);

    HANDLE pipe_read, pipe_write;
    if (!CreatePipe(&pipe_read, &pipe_write, NULL, 0)) {
        return false;
    }

    si.dwFlags = STARTF_USESTDHANDLES;
    si.hStdInput = GetStdHandle(STD_INPUT_HANDLE);
    si.hStdOutput = pipe_write;
    si.hStdError = GetStdHandle(STD_ERROR_HANDLE);

    // Build command line
    size_t len = 0;
    for (int i = 0; argv[i]; ++i) {
        len += strlen(argv[i]) + 3;
    }
    char *cmd = malloc(len);
    if (!cmd) {
        CloseHandle(pipe_read);
        CloseHandle(pipe_write);
        return false;
    }
    char *p = cmd;
    for (int i = 0; argv[i]; ++i) {
        p += sprintf(p, "\"%s\" ", argv[i]);
    }
    if (p > cmd) {
        p[-1] = '\0';
    }

    if (!CreateProcessA(NULL, cmd, NULL, NULL, TRUE, CREATE_NO_WINDOW, NULL, NULL, &si, &pi)) {
        free(cmd);
        CloseHandle(pipe_read);
        CloseHandle(pipe_write);
        return false;
    }

    free(cmd);
    *handle = pi.hProcess;
    *pout = pipe_read;
    CloseHandle(pi.hThread);
    CloseHandle(pipe_write);
    return true;
}

static char *
read_all_from_pipe(HANDLE pipe) {
    char *buffer = NULL;
    size_t size = 0;
    size_t capacity = 0;

    char temp[1024];
    DWORD len;
    while (ReadFile(pipe, temp, sizeof(temp), &len, NULL) && len != 0) {
        if (size + len > capacity) {
            size_t new_cap = capacity == 0 ? sizeof(temp) : capacity * 2;
            if (new_cap < size + len) new_cap = size + len;
            char *new_buf = realloc(buffer, new_cap);
            if (!new_buf) {
                free(buffer);
                return NULL;
            }
            buffer = new_buf;
            capacity = new_cap;
        }
        memcpy(buffer + size, temp, len);
        size += len;
    }

    if (buffer) {
        char *final_buf = realloc(buffer, size + 1);
        if (final_buf) {
            final_buf[size] = '\0';
            return final_buf;
        }
        free(buffer);
    }
    return NULL;
}

#else // POSIX

// POSIX implementation would go here, but we focus on Windows for now

#endif

static SC_PID_T
sc_process_execute(const char *const argv[], bool async) {
    SC_PID_T pid;
#ifdef _WIN32
    if (!windows_process_execute(argv, &pid)) {
        return INVALID_HANDLE_VALUE;
    }
#else
    // POSIX fork/exec
#endif
    
    if (async) {
        return pid;
    }

    process_check_success_internal(pid, argv[0], true);
    return 0; // Return 0 for success in sync mode, though not used
}


// --- ADB commands ---

bool sc_adb_init(void) {
    const char *adb_env = getenv("ADB");
    if (adb_env) {
        adb_path = strdup(adb_env);
        return true;
    }
    // For simplicity, we assume adb is in PATH
    adb_path = "adb";
    return true;
}

SC_PID_T
sc_adb_execute(const char *const argv[], bool show_err_msg) {
    // a bit of a hack to prepend adb_path
    const char **full_argv;
    int i = 0;
    while(argv[i]) i++;
    
    full_argv = malloc(sizeof(char*) * (i + 2));
    if(!full_argv) return INVALID_HANDLE_VALUE;

    full_argv[0] = adb_path;
    for(int j=0; j<i; ++j) {
        full_argv[j+1] = argv[j];
    }
    full_argv[i+1] = NULL;
    
    SC_PID_T pid = sc_process_execute(full_argv, true); // always async for this helper
    free(full_argv);
    
    if (pid == INVALID_HANDLE_VALUE && show_err_msg) {
        fprintf(stderr, "Loi: Khong the chay: %s\n", adb_path);
    }
    return pid;
}

static bool
sc_adb_command_sync(const char *const argv[]) {
    SC_PID_T pid = sc_adb_execute(argv, true);
    return process_check_success_internal(pid, adb_path, true);
}


bool sc_adb_push(const char *serial, const char *local, const char *remote) {
    const char *const argv[] = {
        "-s", serial, "push", local, remote, NULL,
    };
    return sc_adb_command_sync(argv);
}

bool sc_adb_forward(const char *serial, uint16_t local_port, const char *device_socket_name) {
    char local_port_spec[12];
    sprintf(local_port_spec, "tcp:%" PRIu16, local_port);
    char device_socket_spec[128];
    sprintf(device_socket_spec, "localabstract:%s", device_socket_name);

    const char *const argv[] = {
        "-s", serial, "forward", local_port_spec, device_socket_spec, NULL,
    };
    return sc_adb_command_sync(argv);
}

bool sc_adb_forward_remove(const char *serial, uint16_t local_port) {
    char local_port_spec[12];
    sprintf(local_port_spec, "tcp:%" PRIu16, local_port);
    const char *const argv[] = {
        "-s", serial, "forward", "--remove", local_port_spec, NULL,
    };
    return sc_adb_command_sync(argv);
}

SC_PID_T sc_adb_execute_shell_async(const char *serial, const char *const shell_args[]) {
    size_t argc = 0;
    while(shell_args[argc]) argc++;

    const char **argv = malloc(sizeof(char*) * (argc + 4));
    if (!argv) return INVALID_HANDLE_VALUE;

    argv[0] = "-s";
    argv[1] = serial;
    argv[2] = "shell";
    for(size_t i = 0; i < argc; ++i) {
        argv[3+i] = shell_args[i];
    }
    argv[3+argc] = NULL;

    SC_PID_T pid = sc_adb_execute(argv, true);
    free(argv);
    return pid;
}

static SC_PID_T
sc_adb_execute_p(const char *const argv[], bool show_err_msg, HANDLE *pout) {
    const char **full_argv;
    int i = 0;
    while(argv[i]) i++;
    
    full_argv = malloc(sizeof(char*) * (i + 2));
    if(!full_argv) return INVALID_HANDLE_VALUE;

    full_argv[0] = adb_path;
    for(int j=0; j<i; ++j) {
        full_argv[j+1] = argv[j];
    }
    full_argv[i+1] = NULL;
    
    SC_PID_T pid;
    if (!windows_process_execute_p(full_argv, &pid, pout)) {
        pid = INVALID_HANDLE_VALUE;
    }

    free(full_argv);
    
    if (pid == INVALID_HANDLE_VALUE && show_err_msg) {
        fprintf(stderr, "Loi: Khong the chay: %s\n", adb_path);
    }
    return pid;
}

static char*
sc_adb_get_output(const char *const argv[]) {
    HANDLE pipe;
    SC_PID_T pid = sc_adb_execute_p(argv, true, &pipe);
    if (pid == INVALID_HANDLE_VALUE) {
        return NULL;
    }
    char *output = read_all_from_pipe(pipe);
    CloseHandle(pipe);

    if (!process_check_success_internal(pid, argv[0], true)) {
        free(output);
        return NULL;
    }
    return output;
}

// A simplified function to get the first device.
// A real implementation would parse 'adb devices' output.
bool sc_adb_get_first_device_serial(char *serial, size_t len) {
    const char *const argv[] = {"devices", NULL};
    char *output = sc_adb_get_output(argv);
    if (!output) {
        return false;
    }

    char *p = strstr(output, "List of devices attached");
    if (!p) {
        free(output);
        return false;
    }
    
    p = strchr(p, '\n'); // Find the end of the "List of..." line
    if (!p || *p == '\0') {
        free(output);
        return false; // No devices listed
    }
    p++; // Move to the beginning of the next line

    // Skip any empty lines
    while (*p == '\r' || *p == '\n') {
        p++;
    }

    if (*p == '\0') {
        free(output);
        return false; // No devices found
    }

    // Find the end of the serial (it's terminated by a tab or space)
    char *end = p;
    while (*end && *end != '\t' && *end != ' ') {
        end++;
    }

    size_t serial_len = end - p;
    if (serial_len > 0 && serial_len < len) {
        memcpy(serial, p, serial_len);
        serial[serial_len] = '\0';
        free(output);
        return true;
    }

    free(output);
    return false; 
} 