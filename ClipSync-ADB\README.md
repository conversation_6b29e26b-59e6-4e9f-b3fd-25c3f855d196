# ClipSync-ADB

Một phiên bản nhẹ, độc lập của tính năng đồng bộ hóa clipboard từ `scrcpy`, đư<PERSON><PERSON> triển khai bằng Java cho phía Android (server) và C cho phía máy tính (client) trên Windows.

Dự án này cho phép bạn chỉ chạy chức năng đồng bộ hóa clipboard hai chiều giữa máy tính Windows và thiết bị Android được kết nối qua ADB.

## Nguyên lý hoạt động

1.  **Server (Android):** Một tệp `.jar` nhỏ được đẩy lên thiết bị Android. Nó được chạy bằng `app_process` và mở một socket cục bộ. Server lắng nghe các thay đổi clipboard trên thiết bị và gửi chúng đến client. <PERSON>ó cũng nhận văn bản từ client và đặt nó vào clipboard của thiết bị.
2.  **Client (Windows):** Một ứng dụng dòng lệnh C chạy trên Windows.
    *   Nó tự động đẩy và khởi chạy server trên thiết bị được kết nối.
    *   Nó sử dụng `adb forward` để thiết lập kết nối TCP đến server.
    *   Nó khởi động một luồng để theo dõi clipboard của Windows và gửi bất kỳ thay đổi nào đến thiết bị.
    *   Nó lắng nghe trên socket cho các thay đổi từ thiết bị và cập nhật clipboard của Windows.

## Yêu cầu hệ thống

Trước khi xây dựng và chạy, hãy đảm bảo bạn đã cài đặt và cấu hình các công cụ sau:

1.  **ADB:** `adb.exe` phải có trong `PATH` của hệ thống. Bạn có thể tải xuống từ [Android SDK Platform Tools](https://developer.android.com/studio/releases/platform-tools).
2.  **Java Development Kit (JDK):** Cần thiết để xây dựng server. Phiên bản 8 trở lên được khuyến nghị.
    *   Hãy chắc chắn rằng biến môi trường `JAVA_HOME` đã được đặt trỏ đến thư mục cài đặt JDK của bạn.
3.  **CMake:** Cần thiết để xây dựng client. Bạn có thể tải xuống từ [trang web chính thức của CMake](https://cmake.org/download/).
    *   Hãy chắc chắn rằng thư mục `bin` của CMake đã được thêm vào `PATH` của hệ thống trong quá trình cài đặt.
4.  **MinGW-w64:** Cần thiết để biên dịch client C trên Windows. Bạn có thể cài đặt nó qua [MSYS2](https://www.msys2.org/) hoặc tải xuống một bản dựng độc lập.
    *   Hãy chắc chắn rằng thư mục `bin` của MinGW (chứa `mingw32-make.exe` và `gcc.exe`) đã được thêm vào `PATH` của hệ thống.

## Hướng dẫn xây dựng và chạy

### 1. Xây dựng Server (Java)

Mở một terminal hoặc command prompt, điều hướng đến thư mục `server` và chạy lệnh sau:

```bash
# Điều hướng đến thư mục server
cd server

# Chạy lệnh build của Gradle
# Trên Windows
./gradlew.bat build

# Trên macOS/Linux
./gradlew build
```

Lệnh này sẽ biên dịch mã nguồn Java và tạo một tệp jar tại `server/build/libs/clipsync-server-1.0.jar`. Client được cấu hình để tự động tìm tệp này.

*Lưu ý: Nếu lệnh trên thất bại, hãy kiểm tra lại cài đặt JDK và biến môi trường `JAVA_HOME` của bạn.*

### Hướng dẫn Build Thủ công (Khi Gradle Lỗi)

Nếu bạn gặp lỗi khi chạy `gradlew.bat` (ví dụ: `The syntax of the command is incorrect`), bạn có thể build server theo cách thủ công bằng các công cụ dòng lệnh của JDK.

**Yêu cầu:** Biến môi trường `ANDROID_HOME` phải được thiết lập và trỏ đến thư mục Android SDK của bạn.

Mở Command Prompt và thực hiện các lệnh sau:

```cmd
:: Điều hướng đến thư mục gốc của dự án
cd ClipSync-ADB

:: 1. Tạo các thư mục cần thiết cho việc build
if not exist server\bin mkdir server\bin
if not exist server\build\libs mkdir server\build\libs

:: 2. Đặt biến CLASSPATH để bao gồm android.jar
:: Thay thế android-33 bằng phiên bản SDK bạn đã cài đặt nếu cần
set CP=%ANDROID_HOME%\platforms\android-33\android.jar

:: 3. Biên dịch tất cả các tệp Java
:: Cờ -encoding UTF-8 rất quan trọng để xử lý các ký tự đặc biệt
javac -encoding UTF-8 -d server\bin -cp %CP% server\src\android\os\*.java server\src\com\clipsyncadb\*.java server\src\com\clipsyncadb\wrappers\*.java

:: 4. Đóng gói các tệp .class đã biên dịch vào một tệp .jar
jar -cvf server\build\libs\clipsync-server-1.0.jar -C server\bin .

```
Sau khi hoàn tất, tệp `clipsync-server-1.0.jar` sẽ được tạo ở đúng vị trí mà client mong đợi.

### 2. Xây dựng Client (C)

Mở một terminal khác, điều hướng đến thư mục `client`, tạo một thư mục `build`, và chạy CMake và make.

```bash
# Điều hướng đến thư mục client
cd client

# Tạo và vào thư mục build
mkdir build
cd build

# Chạy CMake để tạo các tệp build cho MinGW
# Dấu ".." trỏ CMake đến thư mục cha, nơi chứa CMakeLists.txt
cmake .. -G "MinGW Makefiles"

# Biên dịch mã nguồn bằng MinGW Make
mingw32-make
```

Thao tác này sẽ tạo ra tệp thực thi `clipsyncadb-client.exe` bên trong thư mục `client/build`.

*Lưu ý: Nếu `cmake` hoặc `mingw32-make` không được tìm thấy, hãy kiểm tra lại xem chúng đã được thêm vào `PATH` của hệ thống chưa.*

### 3. Chạy ứng dụng

1.  **Kết nối thiết bị:** Đảm bảo thiết bị Android của bạn đã được kết nối với máy tính và chế độ "USB debugging" đã được bật. Xác minh rằng thiết bị được nhận dạng bằng cách chạy `adb devices`.
2.  **Chạy Client:** Từ thư mục gốc của dự án `ClipSync-ADB`, bạn có thể chạy client như sau:

```bash
# Chạy client từ thư mục gốc
./client/build/clipsyncadb-client.exe
```

Client sẽ tự động tìm thiết bị đầu tiên được kết nối. Nếu bạn có nhiều thiết bị, bạn có thể chỉ định serial của thiết bị làm đối số:

```bash
./client/build/clipsyncadb-client.exe YOUR_DEVICE_SERIAL
```

Ứng dụng sẽ chạy, thực hiện tất cả các bước (đẩy server, chạy server, chuyển tiếp cổng) và bắt đầu đồng bộ hóa. Bạn sẽ thấy thông báo: `*** Đồng bộ Clipboard đang chạy. Nhấn Enter để thoát. ***`.

Để dừng ứng dụng, chỉ cần nhấn `Enter` trong cửa sổ terminal. 