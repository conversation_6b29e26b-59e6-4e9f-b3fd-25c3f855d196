package com.clipsyncadb;

import com.clipsyncadb.wrappers.ClipboardManager;
import com.clipsyncadb.wrappers.ServiceManager;

import java.io.Closeable;
import java.io.IOException;

public final class ClipboardSync implements Closeable {

    private final DesktopConnection connection;
    private final ClipboardManager clipboardManager;
    private long lastSetClipboardSequence = -1;
    private String lastSentClipboardText;

    public ClipboardSync(DesktopConnection connection) {
        this.connection = connection;
        ServiceManager serviceManager = new ServiceManager();
        this.clipboardManager = new ClipboardManager(serviceManager);
    }

    public synchronized void start() {
        if (clipboardManager.hasBrokenClipboard()) {
            return; // Don't start if clipboard service is known to be broken
        }
        clipboardManager.addPrimaryClipChangedListener(this::onPrimaryClipChanged);
        // Initial sync
        onPrimaryClipChanged();
    }

    private synchronized void onPrimaryClipChanged() {
        try {
            String text = clipboardManager.getText();
            if (text != null && !text.equals(lastSentClipboardText)) {
                connection.sendClipboard(text);
                lastSentClipboardText = text;
            }
        } catch (Exception e) {
            // This may happen if the connection is closed, it's expected
        }
    }

    public synchronized void setClipboard(String text, long sequence) {
        if (clipboardManager.setText(text)) {
            this.lastSetClipboardSequence = sequence;
            this.lastSentClipboardText = text;
            System.out.println("Device clipboard set");
        }
    }

    public synchronized long getLastSetClipboardSequence() {
        return lastSetClipboardSequence;
    }

    @Override
    public void close() {
        // DesktopConnection.close() đã tự xử lý IOException bên trong,
        // vì vậy không cần try-catch ở đây.
        connection.close();
    }
} 