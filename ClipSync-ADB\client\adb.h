#ifndef ADB_H
#define ADB_H

#include <stdbool.h>
#include <stdint.h>

#ifdef _WIN32
#include <windows.h>
#define SC_PID_T HANDLE
#else
#include <sys/types.h>
#define SC_PID_T pid_t
#endif

#define SC_ADB_SERIAL_MAX_LENGTH 64

// Khởi tạo và tìm đường dẫn adb
bool sc_adb_init(void);

// Lấy serial của thiết bị đầu tiên tìm thấy
bool sc_adb_get_first_device_serial(char *serial, size_t len);

// Thực thi một lệnh adb
SC_PID_T sc_adb_execute(const char *const argv[], bool show_err_msg);

// Đẩy file lên thiết bị
bool sc_adb_push(const char *serial, const char *local, const char *remote);

// Forward một cổng local tới một socket trên thiết bị
bool sc_adb_forward(const char *serial, uint16_t local_port, const char *device_socket_name);

// Gỡ một forward
bool sc_adb_forward_remove(const char *serial, uint16_t local_port);

// Thực thi một lệnh shell trên thiết bị (không block)
SC_PID_T sc_adb_execute_shell_async(const char *serial, const char *const shell_args[]);


#endif 