package com.clipsyncadb;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;

public final class Server {
    private static final String SOCKET_NAME = "clipsyncadb";

    private Server() {
        // not instantiable
    }

    private static void run() throws IOException {
        final ServerSocket serverSocket = new ServerSocket();
        // Use localabstract socket, which is a Linux/Android-specific feature.
        // It's a socket in a private namespace, identified by a name, not a port number.
        serverSocket.bind(new InetSocketAddress("\0" + SOCKET_NAME, 0));
        System.out.println("Server listening on localabstract:" + SOCKET_NAME);
        
        try {
            while (true) {
                Socket socket = serverSocket.accept();
                System.out.println("Client connected");
                try {
                    // Mỗi client kết nối sẽ có một luồng xử lý riêng
                    DesktopConnection connection = new DesktopConnection(socket);
                    Thread thread = new Thread(connection);
                    thread.start();
                } catch (IOException e) {
                    System.err.println("Could not create connection: " + e.getMessage());
                    socket.close();
                }
            }
        } finally {
            serverSocket.close();
        }
    }

    public static void main(String... args) {
        System.out.println("ClipSyncADB Server starting...");
        try {
            run();
        } catch (Exception e) {
            System.err.println("Server could not start: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 