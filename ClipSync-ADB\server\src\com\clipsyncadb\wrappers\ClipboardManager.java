package com.clipsyncadb.wrappers;

import android.os.IInterface;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

public final class ClipboardManager {

    private final IInterface manager;
    private Method getPrimaryClipMethod;
    private Method setPrimaryClipMethod;
    private Method addPrimaryClipChangedListenerMethod;
    private Method removePrimaryClipChangedListenerMethod;
    
    private boolean brokenClipboard;

    public ClipboardManager(ServiceManager serviceManager) {
        IInterface cb = serviceManager.getService("clipboard", "android.content.IClipboard$Stub");
        if (cb == null) {
            // Some devices have no clipboard service
            brokenClipboard = true;
        }
        this.manager = cb;
        initMethods();
    }

    private void initMethods() {
        if (brokenClipboard) return;
        try {
            getPrimaryClipMethod = manager.getClass().getMethod("getPrimaryClip", String.class);
            setPrimaryClipMethod = manager.getClass().getMethod("setPrimaryClip", Class.forName("android.content.ClipData"), String.class);
            addPrimaryClipChangedListenerMethod = manager.getClass().getMethod("addPrimaryClipChangedListener",
                Class.forName("android.content.IOnPrimaryClipChangedListener"), String.class);
            removePrimaryClipChangedListenerMethod = manager.getClass().getMethod("removePrimaryClipChangedListener",
                Class.forName("android.content.IOnPrimaryClipChangedListener"));

        } catch (Exception e) {
            System.err.println("Could not initialize clipboard methods");
            brokenClipboard = true;
        }
    }
    
    public boolean hasBrokenClipboard() {
        return brokenClipboard;
    }

    private String getPackageName() {
        return "com.android.shell";
    }

    public String getText() {
        if (brokenClipboard) return null;
        try {
            Object clipData = getPrimaryClipMethod.invoke(manager, getPackageName());
            if (clipData == null) {
                return null;
            }
            Method getItemAt = clipData.getClass().getMethod("getItemAt", int.class);
            Object item = getItemAt.invoke(clipData, 0);
            if (item == null) {
                return null;
            }
            Method getText = item.getClass().getMethod("getText");
            CharSequence s = (CharSequence) getText.invoke(item);
            return s == null ? null : s.toString();
        } catch (Exception e) {
            System.err.println("Could not get clipboard text");
            return null;
        }
    }

    public boolean setText(CharSequence text) {
        if (brokenClipboard) return false;
        try {
            Class<?> clipDataClass = Class.forName("android.content.ClipData");
            Method newPlainText = clipDataClass.getMethod("newPlainText", CharSequence.class, CharSequence.class);
            Object clip = newPlainText.invoke(null, "label", text);
            setPrimaryClipMethod.invoke(manager, clip, getPackageName());
            return true;
        } catch (Exception e) {
            System.err.println("Could not set clipboard text");
            return false;
        }
    }
    
    public void addPrimaryClipChangedListener(final Runnable runnable) {
        if (brokenClipboard) return;
        try {
            // Create a proxy for the listener interface
            InvocationHandler handler = (proxy, method, args) -> {
                runnable.run();
                return null;
            };
            Class<?> listenerClass = Class.forName("android.content.IOnPrimaryClipChangedListener");
            Object listener = Proxy.newProxyInstance(listenerClass.getClassLoader(), new Class<?>[]{listenerClass}, handler);

            addPrimaryClipChangedListenerMethod.invoke(manager, listener, getPackageName());
        } catch (Exception e) {
             System.err.println("Could not add clipboard listener");
        }
    }
} 