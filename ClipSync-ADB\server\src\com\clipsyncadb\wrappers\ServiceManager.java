package com.clipsyncadb.wrappers;

import android.os.IBinder;
import android.os.IInterface;

import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

public final class ServiceManager {

    private final Method getServiceMethod;

    public ServiceManager() {
        try {
            getServiceMethod = Class.forName("android.os.ServiceManager").getDeclaredMethod("getService", String.class);
        } catch (Exception e) {
            throw new AssertionError(e);
        }
    }

    private IBinder getService(String name) {
        try {
            return (IBinder) getServiceMethod.invoke(null, name);
        } catch (Exception e) {
            throw new AssertionError(e);
        }
    }

    public IInterface getService(String name, String type) {
        try {
            IBinder binder = getService(name);
            if (binder == null) {
                return null;
            }
            Class<?> stubClass = Class.forName(type);
            Method asInterfaceMethod = stubClass.getMethod("asInterface", IBinder.class);
            return (IInterface) asInterfaceMethod.invoke(null, binder);
        } catch (Exception e) {
            // Service not found
            return null;
        }
    }
} 