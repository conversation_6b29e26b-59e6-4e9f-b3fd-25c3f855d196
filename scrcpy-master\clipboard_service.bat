@echo off
REM Clipboard Service cho Windows CMD
REM Chạy scrcpy với clipboard monitoring trong background

echo ========================================
echo Scrcpy Clipboard Service
echo ========================================
echo.

REM Kiểm tra file scrcpy.exe có tồn tại không
if not exist "build-win64-native\app\scrcpy.exe" (
    echo ERROR: Không tìm thấy scrcpy.exe
    echo Vui lòng build scrcpy trước khi chạy script này
    echo.
    echo Chạy lệnh sau trong MINGW64 terminal:
    echo   meson compile -C build-win64-native
    echo.
    pause
    exit /b 1
)

REM Kiểm tra ADB có trong PATH không
where adb >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: ADB không có trong PATH
    echo Đang thử sử dụng ADB từ Android SDK...
    
    REM Thử tìm ADB trong các vị trí phổ biến
    if exist "D:\Android\GT_Neo2\platform-tools\adb.exe" (
        set "ADB_PATH=D:\Android\GT_Neo2\platform-tools"
        echo Tìm thấy ADB tại: %ADB_PATH%
    ) else if exist "C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools\adb.exe" (
        set "ADB_PATH=C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools"
        echo Tìm thấy ADB tại: %ADB_PATH%
    ) else (
        echo ERROR: Không tìm thấy ADB
        echo Vui lòng cài đặt Android SDK hoặc thêm ADB vào PATH
        pause
        exit /b 1
    )
    
    REM Thêm ADB vào PATH tạm thời
    set "PATH=%PATH%;%ADB_PATH%"
)

REM Thêm MINGW64 DLLs vào PATH
if exist "C:\msys64\mingw64\bin" (
    set "PATH=%PATH%;C:\msys64\mingw64\bin"
    echo Đã thêm MINGW64 libraries vào PATH
) else (
    echo WARNING: Không tìm thấy MINGW64 libraries
    echo Có thể gặp lỗi thiếu DLL khi chạy scrcpy
)

REM Kiểm tra device có kết nối không
echo Đang kiểm tra Android device...
adb devices | findstr "device" | findstr -v "List" >nul
if %errorlevel% neq 0 (
    echo WARNING: Không tìm thấy Android device
    echo.
    echo Vui lòng:
    echo 1. Kết nối Android device qua USB
    echo 2. Bật USB debugging trên Android
    echo 3. Authorize máy tính khi popup xuất hiện
    echo 4. Chạy 'adb devices' để kiểm tra
    echo.
    echo Bạn có muốn tiếp tục không? (y/n)
    set /p choice=
    if /i not "%choice%"=="y" exit /b 1
)

echo.
echo Chọn chế độ chạy:
echo 1. Clipboard-only mode (khuyến nghị - ít tài nguyên)
echo 2. Normal mode với clipboard monitoring
echo 3. Background service (window nhỏ)
echo.
set /p mode="Nhập lựa chọn (1-3): "

if "%mode%"=="1" (
    echo.
    echo Đang khởi động Clipboard-only mode...
    echo Chế độ này chỉ đồng bộ clipboard, không có video/audio
    echo Nhấn Ctrl+C để thoát
    echo.
    build-win64-native\app\scrcpy.exe --clipboard-only
) else if "%mode%"=="2" (
    echo.
    echo Đang khởi động Normal mode với clipboard monitoring...
    echo Chế độ này có đầy đủ video/audio + clipboard sync
    echo.
    build-win64-native\app\scrcpy.exe --clipboard-monitor
) else if "%mode%"=="3" (
    echo.
    echo Đang khởi động Background service...
    echo Chế độ này chạy với window nhỏ trong background
    echo.
    start /min "Scrcpy Clipboard Service" build-win64-native\app\scrcpy.exe --clipboard-monitor --window-width=320 --window-height=240 --no-audio --window-title="Clipboard Sync"
    echo.
    echo Background service đã được khởi động!
    echo Kiểm tra taskbar để thấy window nhỏ
    echo.
    pause
) else (
    echo Lựa chọn không hợp lệ!
    pause
    exit /b 1
)
