# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = C:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build

# Include any dependencies generated for this target.
include CMakeFiles/clipsyncadb-client.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/clipsyncadb-client.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/clipsyncadb-client.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/clipsyncadb-client.dir/flags.make

CMakeFiles/clipsyncadb-client.dir/codegen:
.PHONY : CMakeFiles/clipsyncadb-client.dir/codegen

CMakeFiles/clipsyncadb-client.dir/main.c.obj: CMakeFiles/clipsyncadb-client.dir/flags.make
CMakeFiles/clipsyncadb-client.dir/main.c.obj: C:/Users/<USER>/Music/scrcpy/ClipSync-ADB/client/main.c
CMakeFiles/clipsyncadb-client.dir/main.c.obj: CMakeFiles/clipsyncadb-client.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/clipsyncadb-client.dir/main.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/clipsyncadb-client.dir/main.c.obj -MF CMakeFiles\clipsyncadb-client.dir\main.c.obj.d -o CMakeFiles\clipsyncadb-client.dir\main.c.obj -c C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\main.c

CMakeFiles/clipsyncadb-client.dir/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/clipsyncadb-client.dir/main.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\main.c > CMakeFiles\clipsyncadb-client.dir\main.c.i

CMakeFiles/clipsyncadb-client.dir/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/clipsyncadb-client.dir/main.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\main.c -o CMakeFiles\clipsyncadb-client.dir\main.c.s

CMakeFiles/clipsyncadb-client.dir/adb.c.obj: CMakeFiles/clipsyncadb-client.dir/flags.make
CMakeFiles/clipsyncadb-client.dir/adb.c.obj: C:/Users/<USER>/Music/scrcpy/ClipSync-ADB/client/adb.c
CMakeFiles/clipsyncadb-client.dir/adb.c.obj: CMakeFiles/clipsyncadb-client.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/clipsyncadb-client.dir/adb.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/clipsyncadb-client.dir/adb.c.obj -MF CMakeFiles\clipsyncadb-client.dir\adb.c.obj.d -o CMakeFiles\clipsyncadb-client.dir\adb.c.obj -c C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\adb.c

CMakeFiles/clipsyncadb-client.dir/adb.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/clipsyncadb-client.dir/adb.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\adb.c > CMakeFiles\clipsyncadb-client.dir\adb.c.i

CMakeFiles/clipsyncadb-client.dir/adb.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/clipsyncadb-client.dir/adb.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\adb.c -o CMakeFiles\clipsyncadb-client.dir\adb.c.s

CMakeFiles/clipsyncadb-client.dir/sockets.c.obj: CMakeFiles/clipsyncadb-client.dir/flags.make
CMakeFiles/clipsyncadb-client.dir/sockets.c.obj: C:/Users/<USER>/Music/scrcpy/ClipSync-ADB/client/sockets.c
CMakeFiles/clipsyncadb-client.dir/sockets.c.obj: CMakeFiles/clipsyncadb-client.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/clipsyncadb-client.dir/sockets.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/clipsyncadb-client.dir/sockets.c.obj -MF CMakeFiles\clipsyncadb-client.dir\sockets.c.obj.d -o CMakeFiles\clipsyncadb-client.dir\sockets.c.obj -c C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\sockets.c

CMakeFiles/clipsyncadb-client.dir/sockets.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/clipsyncadb-client.dir/sockets.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\sockets.c > CMakeFiles\clipsyncadb-client.dir\sockets.c.i

CMakeFiles/clipsyncadb-client.dir/sockets.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/clipsyncadb-client.dir/sockets.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\sockets.c -o CMakeFiles\clipsyncadb-client.dir\sockets.c.s

CMakeFiles/clipsyncadb-client.dir/clipboard.c.obj: CMakeFiles/clipsyncadb-client.dir/flags.make
CMakeFiles/clipsyncadb-client.dir/clipboard.c.obj: C:/Users/<USER>/Music/scrcpy/ClipSync-ADB/client/clipboard.c
CMakeFiles/clipsyncadb-client.dir/clipboard.c.obj: CMakeFiles/clipsyncadb-client.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/clipsyncadb-client.dir/clipboard.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/clipsyncadb-client.dir/clipboard.c.obj -MF CMakeFiles\clipsyncadb-client.dir\clipboard.c.obj.d -o CMakeFiles\clipsyncadb-client.dir\clipboard.c.obj -c C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\clipboard.c

CMakeFiles/clipsyncadb-client.dir/clipboard.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/clipsyncadb-client.dir/clipboard.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\clipboard.c > CMakeFiles\clipsyncadb-client.dir\clipboard.c.i

CMakeFiles/clipsyncadb-client.dir/clipboard.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/clipsyncadb-client.dir/clipboard.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\clipboard.c -o CMakeFiles\clipsyncadb-client.dir\clipboard.c.s

CMakeFiles/clipsyncadb-client.dir/communication.c.obj: CMakeFiles/clipsyncadb-client.dir/flags.make
CMakeFiles/clipsyncadb-client.dir/communication.c.obj: C:/Users/<USER>/Music/scrcpy/ClipSync-ADB/client/communication.c
CMakeFiles/clipsyncadb-client.dir/communication.c.obj: CMakeFiles/clipsyncadb-client.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/clipsyncadb-client.dir/communication.c.obj"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/clipsyncadb-client.dir/communication.c.obj -MF CMakeFiles\clipsyncadb-client.dir\communication.c.obj.d -o CMakeFiles\clipsyncadb-client.dir\communication.c.obj -c C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\communication.c

CMakeFiles/clipsyncadb-client.dir/communication.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/clipsyncadb-client.dir/communication.c.i"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\communication.c > CMakeFiles\clipsyncadb-client.dir\communication.c.i

CMakeFiles/clipsyncadb-client.dir/communication.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/clipsyncadb-client.dir/communication.c.s"
	C:\msys64\mingw64\bin\cc.exe $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\communication.c -o CMakeFiles\clipsyncadb-client.dir\communication.c.s

# Object files for target clipsyncadb-client
clipsyncadb__client_OBJECTS = \
"CMakeFiles/clipsyncadb-client.dir/main.c.obj" \
"CMakeFiles/clipsyncadb-client.dir/adb.c.obj" \
"CMakeFiles/clipsyncadb-client.dir/sockets.c.obj" \
"CMakeFiles/clipsyncadb-client.dir/clipboard.c.obj" \
"CMakeFiles/clipsyncadb-client.dir/communication.c.obj"

# External object files for target clipsyncadb-client
clipsyncadb__client_EXTERNAL_OBJECTS =

clipsyncadb-client.exe: CMakeFiles/clipsyncadb-client.dir/main.c.obj
clipsyncadb-client.exe: CMakeFiles/clipsyncadb-client.dir/adb.c.obj
clipsyncadb-client.exe: CMakeFiles/clipsyncadb-client.dir/sockets.c.obj
clipsyncadb-client.exe: CMakeFiles/clipsyncadb-client.dir/clipboard.c.obj
clipsyncadb-client.exe: CMakeFiles/clipsyncadb-client.dir/communication.c.obj
clipsyncadb-client.exe: CMakeFiles/clipsyncadb-client.dir/build.make
clipsyncadb-client.exe: CMakeFiles/clipsyncadb-client.dir/linkLibs.rsp
clipsyncadb-client.exe: CMakeFiles/clipsyncadb-client.dir/objects1.rsp
clipsyncadb-client.exe: CMakeFiles/clipsyncadb-client.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Linking C executable clipsyncadb-client.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\clipsyncadb-client.dir\link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/clipsyncadb-client.dir/build: clipsyncadb-client.exe
.PHONY : CMakeFiles/clipsyncadb-client.dir/build

CMakeFiles/clipsyncadb-client.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\clipsyncadb-client.dir\cmake_clean.cmake
.PHONY : CMakeFiles/clipsyncadb-client.dir/clean

CMakeFiles/clipsyncadb-client.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build C:\Users\<USER>\Music\scrcpy\ClipSync-ADB\client\build\CMakeFiles\clipsyncadb-client.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/clipsyncadb-client.dir/depend

