
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "C:/Users/<USER>/Music/scrcpy/ClipSync-ADB/client/adb.c" "CMakeFiles/clipsyncadb-client.dir/adb.c.obj" "gcc" "CMakeFiles/clipsyncadb-client.dir/adb.c.obj.d"
  "C:/Users/<USER>/Music/scrcpy/ClipSync-ADB/client/clipboard.c" "CMakeFiles/clipsyncadb-client.dir/clipboard.c.obj" "gcc" "CMakeFiles/clipsyncadb-client.dir/clipboard.c.obj.d"
  "C:/Users/<USER>/Music/scrcpy/ClipSync-ADB/client/communication.c" "CMakeFiles/clipsyncadb-client.dir/communication.c.obj" "gcc" "CMakeFiles/clipsyncadb-client.dir/communication.c.obj.d"
  "C:/Users/<USER>/Music/scrcpy/ClipSync-ADB/client/main.c" "CMakeFiles/clipsyncadb-client.dir/main.c.obj" "gcc" "CMakeFiles/clipsyncadb-client.dir/main.c.obj.d"
  "C:/Users/<USER>/Music/scrcpy/ClipSync-ADB/client/sockets.c" "CMakeFiles/clipsyncadb-client.dir/sockets.c.obj" "gcc" "CMakeFiles/clipsyncadb-client.dir/sockets.c.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
