#ifndef LOG_H
#define LOG_H

#include <stdio.h>
#include <time.h>
#include <windows.h>

// Log levels
typedef enum {
    LOG_LEVEL_DEBUG = 0,
    LOG_LEVEL_INFO = 1,
    LOG_LEVEL_WARN = 2,
    LOG_LEVEL_ERROR = 3
} log_level_t;

// Current log level (có thể thay đổi để control verbosity)
extern log_level_t g_log_level;

// Macro để log với timestamp
#define LOG(level, format, ...) \
    do { \
        if (level >= g_log_level) { \
            log_with_timestamp(level, __FILE__, __LINE__, format, ##__VA_ARGS__); \
        } \
    } while(0)

#define LOGD(format, ...) LOG(LOG_LEVEL_DEBUG, format, ##__VA_ARGS__)
#define LOGI(format, ...) LOG(LOG_LEVEL_INFO, format, ##__VA_ARGS__)
#define LOGW(format, ...) LOG(LOG_LEVEL_WARN, format, ##__VA_ARGS__)
#define LOGE(format, ...) LOG(LOG_LEVEL_ERROR, format, ##__VA_ARGS__)

// Function để log với timestamp
void log_with_timestamp(log_level_t level, const char *file, int line, const char *format, ...);

// Function để set log level
void log_set_level(log_level_t level);

// Function để get readable error message từ Windows error code
const char* get_windows_error_message(DWORD error_code);

#endif
