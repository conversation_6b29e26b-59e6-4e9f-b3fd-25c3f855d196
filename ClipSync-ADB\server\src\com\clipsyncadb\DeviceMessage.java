package com.clipsyncadb;

import java.io.EOFException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;

public final class DeviceMessage {

    public static final int TYPE_SET_CLIPBOARD = 0;
    private static final int MAX_CLIPBOARD_TEXT_LENGTH = 262144; // 256k

    private final int type;
    private final String text;
    private long sequence;

    private DeviceMessage(int type, String text, long sequence) {
        this.type = type;
        this.text = text;
        this.sequence = sequence;
    }

    public int getType() {
        return type;
    }

    public String getText() {
        return text;
    }

    public long getSequence() {
        return sequence;
    }

    private static String readUTF8(InputStream stream, int len) throws IOException {
        byte[] buffer = new byte[len];
        int read = 0;
        while (read < len) {
            int r = stream.read(buffer, read, len - read);
            if (r == -1) {
                throw new EOFException("Kênh giao tiếp bị đóng");
            }
            read += r;
        }
        return new String(buffer, StandardCharsets.UTF_8);
    }

    /**
     * Đọc và giải mã một thông điệp từ stream.
     * @param stream Stream đầu vào
     * @return Thông điệp đã được giải mã, hoặc null nếu hết stream.
     * @throws IOException
     */
    public static DeviceMessage from(InputStream stream) throws IOException {
        byte[] typeBytes = new byte[1];
        int r = stream.read(typeBytes);
        if (r == -1) {
            return null; // Hết stream
        }
        int type = typeBytes[0];

        switch (type) {
            case TYPE_SET_CLIPBOARD:
                byte[] seqBytes = new byte[8];
                if (stream.read(seqBytes) < 8) {
                    throw new IOException("Không thể đọc sequence clipboard");
                }
                long sequence = ByteBuffer.wrap(seqBytes).getLong();
                
                // Bỏ qua 1 byte của cờ "paste" mà client gửi
                if (stream.read() == -1) {
                    throw new IOException("Không thể đọc cờ paste");
                }

                byte[] lenBytes = new byte[4];
                if (stream.read(lenBytes) < 4) {
                    throw new IOException("Không thể đọc độ dài clipboard");
                }
                int len = ByteBuffer.wrap(lenBytes).getInt();
                if (len > MAX_CLIPBOARD_TEXT_LENGTH) {
                    throw new IOException("Văn bản clipboard quá dài");
                }
                String text = readUTF8(stream, len);
                return new DeviceMessage(TYPE_SET_CLIPBOARD, text, sequence);
            default:
                System.err.println("Loại thông điệp không xác định: " + type);
                return null;
        }
    }
} 